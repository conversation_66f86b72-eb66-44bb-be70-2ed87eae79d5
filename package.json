{"name": "vox-vitae", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@vercel/analytics": "^1.5.0", "framer-motion": "^11.0.0", "lucide-react": "^0.525.0", "next": "latest", "openai": "^5.16.0", "react": "latest", "react-dom": "latest"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.4.0", "eslint": "^8.0.0", "eslint-config-next": "latest", "postcss": "^8.4.0", "tailwindcss": "^3.4.0", "typescript": "^5.0.0"}}