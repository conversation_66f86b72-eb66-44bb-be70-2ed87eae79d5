'use client'

import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { Home } from 'lucide-react'

export function HomeButton() {
  const router = useRouter()

  const handleHomeClick = () => {
    router.push('/')
  }

  return (
    <motion.button
      onClick={handleHomeClick}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      className="flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
      title="Go to Home"
    >
      <Home size={20} />
      <span>Home</span>
    </motion.button>
  )
} 