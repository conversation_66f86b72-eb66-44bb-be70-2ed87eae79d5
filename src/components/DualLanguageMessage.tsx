'use client'

import { useEffect, useCallback, useState, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useDualLanguageParser } from '@/hooks/useDualLanguageParser'
import { 
  DualLanguageMessageProps, 
  SentenceEvent, 
  SentenceData,
  ParserConfig 
} from '@/types/dual-language'

// Default configuration for the parser
const defaultConfig: Partial<ParserConfig> = {
  translationDelay: 2000,
  enableDebugMode: true,
  fallbackToOriginal: true,
  sentenceMinLength: 3,
  translationTimeout: 500
}

export function DualLanguageMessage({ 
  content, 
  mvp, 
  isStreaming = false,
  config = {},
  onSentenceEvent,
  onParsingError,
  debugMode = false
}: DualLanguageMessageProps) {
  // Merge configuration
  const mergedConfig = { ...defaultConfig, ...config, enableDebugMode: debugMode }
  
  // State for tracking which sentences should show English
  const [translatedSentences, setTranslatedSentences] = useState<Set<string>>(new Set())
  
  // Track the last mvpId to prevent unnecessary resets
  const lastMvpIdRef = useRef<string | null>(null)
  
  // Handle sentence events
  const handleSentenceEvent = useCallback((event: SentenceEvent) => {
    switch (event.type) {
      case 'sentence_completed':
        // Sentence is complete, schedule translation
        setTimeout(() => {
          setTranslatedSentences(prev => new Set(Array.from(prev).concat(event.sentenceId)))
        }, 0) // Immediate translation
        break
        
      case 'translation_started':
        // Start showing English for this sentence
        setTranslatedSentences(prev => new Set(Array.from(prev).concat(event.sentenceId)))
        break
        
      case 'sentence_translated':
        // Translation animation complete
        break
        
      case 'translation_failed':
        if (debugMode) {
          console.warn('Translation failed for sentence:', event.sentenceId, event.error)
        }
        break
        
      case 'parsing_error':
        if (debugMode) {
          console.error('Parsing error:', event.error)
        }
        break
    }
    
    // Call external event handler
    onSentenceEvent?.(event)
  }, [debugMode, onSentenceEvent])

  // Handle parsing errors
  const handleParsingError = useCallback((error: string) => {
    if (debugMode) {
      console.error('DualLanguageMessage: Parsing error', error)
    }
    onParsingError?.(error)
  }, [debugMode, onParsingError])

  // Use the dual language parser hook
  const {
    streamingMessage,
    updateContent,
    setStreamingComplete,
    reset,
    getDebugInfo
  } = useDualLanguageParser(
    mvp,
    mergedConfig,
    handleSentenceEvent,
    handleParsingError
  )

  // Update content when props change
  useEffect(() => {
    updateContent(content)
  }, [content, updateContent, mvp.id, isStreaming])

  // Handle streaming completion
  useEffect(() => {
    if (!isStreaming && streamingMessage.phase === 'streaming') {
      setStreamingComplete()
    }
  }, [isStreaming, streamingMessage.phase, setStreamingComplete])

  // Reset when mvpId changes
  useEffect(() => {
    if (lastMvpIdRef.current !== mvp.id) {
      lastMvpIdRef.current = mvp.id
      setTranslatedSentences(new Set())
      reset()
    }
  }, [mvp.id, reset])

  // Debug logging
  useEffect(() => {
    if (debugMode) {
      console.log('DualLanguageMessage state:', {
        phase: streamingMessage.phase,
        parsingState: streamingMessage.parsingState,
        sentenceCount: streamingMessage.sentences.length,
        translatedCount: translatedSentences.size,
        hasValidFormat: streamingMessage.parsedContent.hasValidFormat,
        debugInfo: getDebugInfo()
      })
    }
  }, [streamingMessage, translatedSentences, debugMode, getDebugInfo])

  // Render fallback if no sentences
  if (streamingMessage.sentences.length === 0) {
    return (
      <div className="text-gray-200 leading-relaxed whitespace-pre-wrap">
        {content || 'Loading...'}
        {debugMode && (
          <div className="mt-2 text-xs text-gray-500 border-t pt-2">
            Debug: No sentences parsed yet
          </div>
        )}
      </div>
    )
  }

  // Render error state
  if (streamingMessage.parsingState === 'error') {
    return (
      <div className="text-gray-200 leading-relaxed whitespace-pre-wrap">
        {streamingMessage.parsedContent.originalText || content}
        {debugMode && (
          <div className="mt-2 text-xs text-red-400 border-t pt-2">
            Debug: Parsing errors - {streamingMessage.parsedContent.parseErrors.join(', ')}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="relative">
      {/* Debug info */}
      {debugMode && (
        <div className="mb-2 text-xs text-blue-400 border border-blue-400/20 rounded p-2">
          <div>Phase: {streamingMessage.phase} | State: {streamingMessage.parsingState}</div>
          <div>Sentences: {streamingMessage.sentences.length} | Translated: {translatedSentences.size}</div>
          <div>Valid Format: {streamingMessage.parsedContent.hasValidFormat ? 'Yes' : 'No'}</div>
        </div>
      )}
      
      {/* Sentences */}
      <div className="space-y-1">
        {streamingMessage.sentences.map((sentence: SentenceData) => {
          const shouldShowEnglish = translatedSentences.has(sentence.id)
          const isTranslating = sentence.state === 'translated' && !shouldShowEnglish
          
          return (
            <SentenceRenderer
              key={sentence.id}
              sentence={sentence}
              showEnglish={shouldShowEnglish}
              isTranslating={isTranslating}
              debugMode={debugMode}
            />
          )
        })}
      </div>
    </div>
  )
}

// Separate component for rendering individual sentences
interface SentenceRendererProps {
  sentence: SentenceData
  showEnglish: boolean
  isTranslating: boolean
  debugMode: boolean
}

function SentenceRenderer({ 
  sentence, 
  showEnglish, 
  isTranslating, 
  debugMode 
}: SentenceRendererProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 5 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
      className="relative"
    >
      {/* Debug info for sentence */}
      {debugMode && (
        <div className="text-xs text-gray-500 mb-1">
          {sentence.id} | {sentence.state} | Complete: {sentence.isComplete ? 'Yes' : 'No'} | ShowEng: {showEnglish ? 'Yes' : 'No'}
        </div>
      )}
      
      <AnimatePresence mode="wait">
        {!showEnglish ? (
          <motion.span
            key={`${sentence.id}-original`}
            initial={{ opacity: 1 }}
            exit={{ opacity: 0, x: -10 }}
            transition={{ duration: 0.4 }}
            className={`text-gray-200 leading-relaxed ${
              isTranslating ? 'opacity-75' : ''
            }`}
          >
            {sentence.originalText}
            {!sentence.isComplete && (
              <motion.span
                animate={{ opacity: [1, 0.3, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
                className="ml-1 text-gray-400"
              >
                ▋
              </motion.span>
            )}
          </motion.span>
        ) : (
          <motion.span
            key={`${sentence.id}-english`}
            initial={{ opacity: 0, x: 10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4 }}
            className="text-gray-200 leading-relaxed"
          >
            {sentence.englishText || sentence.originalText}
          </motion.span>
        )}
      </AnimatePresence>
      
      {/* Translation loading indicator */}
      {isTranslating && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className="absolute -right-6 top-0 text-blue-400"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="w-3 h-3 border border-blue-400 border-t-transparent rounded-full"
          />
        </motion.div>
      )}
    </motion.div>
  )
}