'use client'

import { motion } from 'framer-motion'

interface SimpleTextMessageProps {
  content: string
  isStreaming?: boolean
}

export function SimpleTextMessage({ content, isStreaming = false }: SimpleTextMessageProps) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="text-gray-200 leading-relaxed whitespace-pre-wrap"
    >
      {content}
      {isStreaming && content && (
        <motion.span
          animate={{ opacity: [1, 0.3, 1] }}
          transition={{ duration: 1, repeat: Infinity }}
          className="ml-1 text-gray-400"
        >
          ▋
        </motion.span>
      )}
    </motion.div>
  )
} 