'use client'

import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { track } from '@vercel/analytics'
import { <PERSON> } from '@/data/mvps'
import { Category } from '@/data/categories'
import { useLayoutTransition } from '@/contexts/LayoutTransitionContext'

interface MVPCardProps {
  mvp: MVP
  index: number
  category: Category
}

export function MVPCard({ mvp, index, category }: MVPCardProps) {
  const router = useRouter()
  const { startTransition } = useLayoutTransition()

  const handleMVPClick = () => {
    // Track MVP card click
    track('mvp_card_clicked', {
      mvp_id: mvp.id,
      mvp_name: mvp.name,
      category_id: category.id,
      category_name: category.name,
      birth_year: mvp.birthYear,
      death_year: mvp.deathYear
    })
    
    startTransition(mvp, category)
    // Small delay to allow transition to start before navigation
    setTimeout(() => {
      router.push(`/chat/${mvp.id}`)
    }, 100)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1, duration: 0.5 }}
      whileHover={{ y: -8, scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={handleMVPClick}
      className="cursor-pointer group"
    >
      <motion.div 
        layoutId={`mvp-card-${mvp.id}`}
        className={`
          relative overflow-hidden rounded-2xl p-6 h-80
          bg-gradient-to-br ${category.bgGradient}
          border border-white/20 dark:border-gray-600/20 shadow-lg
          hover:shadow-2xl transition-all duration-300
          group-hover:border-white/40 dark:group-hover:border-gray-400/40
        `}>
        {/* Floating Animation */}
        <motion.div
          className="absolute inset-0 opacity-10"
          animate={{
            background: [
              'radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.3) 0%, transparent 50%)',
              'radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.3) 0%, transparent 50%)',
              'radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.3) 0%, transparent 50%)',
            ],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Content */}
        <div className="relative z-10 flex flex-col items-center text-center h-full">
          {/* Avatar */}
          <motion.div
            layoutId={`mvp-avatar-${mvp.id}`}
            className="text-6xl mb-4"
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            {mvp.avatar}
          </motion.div>

          {/* Name */}
          <motion.h3 
            layoutId={`mvp-name-${mvp.id}`}
            className={`text-xl font-bold mb-2 text-gray-900 dark:text-white`}
          >
            {mvp.name}
          </motion.h3>

          {/* Title */}
          <p className="text-sm text-gray-900 dark:text-white mb-3 font-medium">
            {mvp.title}
          </p>

          {/* Era */}
          <div className="bg-white/80 dark:bg-gray-800/80 rounded-full px-3 py-1 text-xs text-gray-800 dark:text-gray-200 mb-4">
            {mvp.era}
          </div>

          {/* Description */}
          <p className="text-xs text-gray-800 dark:text-gray-200 leading-relaxed line-clamp-3 flex-grow">
            {mvp.description}
          </p>

          {/* Hover Effect */}
          <motion.div
            className="absolute inset-0 bg-white/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            whileHover={{ scale: 1.02 }}
          />
        </div>

        {/* Subtle Border Animation */}
        <motion.div
          className="absolute inset-0 rounded-2xl border-2 border-transparent"
          animate={{
            borderColor: [
              'rgba(255, 255, 255, 0.1)',
              'rgba(255, 255, 255, 0.3)',
              'rgba(255, 255, 255, 0.1)',
            ],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </motion.div>
    </motion.div>
  )
}