'use client'

import { motion } from 'framer-motion'
import { <PERSON> } from '@/data/mvps'

interface TypingIndicatorProps {
  mvp: MVP
}

export function TypingIndicator({ mvp }: TypingIndicatorProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="flex items-start gap-3 mb-4"
    >
      {/* Avatar */}
      <div className="flex-shrink-0 w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-lg text-white">
        {mvp.avatar}
      </div>

      {/* Typing Bubble */}
      <motion.div
        initial={{ scale: 0.8 }}
        animate={{ scale: 1 }}
        transition={{ type: "spring", stiffness: 300 }}
        className="relative bg-white px-4 py-3 rounded-2xl rounded-bl-sm shadow-md border border-gray-100"
      >
        <div className="flex items-center gap-1">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className="w-2 h-2 bg-gray-400 rounded-full"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.2,
                ease: "easeInOut",
              }}
            />
          ))}
        </div>
        
        {/* Message tail */}
        <div className="absolute left-0 top-0 w-0 h-0 border-r-8 border-r-white border-t-8 border-t-transparent" />
      </motion.div>
    </motion.div>
  )
}