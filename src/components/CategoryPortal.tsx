'use client'

import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useMemo } from 'react'
import { track } from '@vercel/analytics'
import { Category } from '@/data/categories'
import { useCategoryTransition } from '@/contexts/CategoryTransitionContext'

interface CategoryPortalProps {
  category: Category
  index: number
}

export function CategoryPortal({ category, index }: CategoryPortalProps) {
  const router = useRouter()
  const { startCategoryTransition, isTransitioning, transitionCategory } = useCategoryTransition()
  
  // Check if this specific category is the one being transitioned from
  const isThisCategoryTransitioning = isTransitioning && transitionCategory?.id === category.id
  
  // Debug logging
  console.log(`CategoryPortal ${category.id}:`, {
    isTransitioning,
    transitionCategoryId: transitionCategory?.id,
    isThisCategoryTransitioning,
    categoryId: category.id
  })

  // Memoize animation configuration to prevent restarts
  const iconAnimation = useMemo(() => {
    if (isThisCategoryTransitioning) {
      return undefined // Let layout animation handle it
    }
    return { 
      rotate: [0, 10, 0],
      y: [0, -5, 0]
    }
  }, [isThisCategoryTransitioning])

  const iconTransition = useMemo(() => ({
    duration: 4,
    repeat: Infinity,
    ease: "easeInOut" as const
  }), [])

  const handleCategoryClick = () => {
    // Track category portal click
    track('category_portal_clicked', {
      category_id: category.id,
      category_name: category.name,
      from_homepage: true
    })
    
    // Start the transition
    startCategoryTransition(category)
    
    // Small delay to allow transition to start before navigation
    setTimeout(() => {
      router.push(`/category/${category.id}`)
    }, 100)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1, duration: 0.6 }}
      className="portal-3d"
    >
      <motion.div
        whileHover={{ scale: 1.05, rotateX: 5, rotateY: 5 }}
        whileTap={{ scale: 0.95 }}
        onClick={handleCategoryClick}
        className={`
          portal-card cursor-pointer relative overflow-hidden
          bg-gradient-to-br ${category.bgGradient} 
          rounded-2xl p-8 h-64 
          shadow-lg hover:shadow-2xl
          border border-white/20
          transition-all duration-300
        `}
      >
        {/* Floating Icons */}
        <div className="absolute inset-0 pointer-events-none">
          {category.figures.map((figure, figureIndex) => (
            <motion.div
              key={figureIndex}
              className="absolute text-2xl opacity-20"
              style={{
                left: `${20 + figureIndex * 15}%`,
                top: `${10 + figureIndex * 12}%`,
              }}
              animate={{
                y: [0, -10, 0],
                rotate: [0, 5, 0],
              }}
              transition={{
                duration: 3,
                delay: figureIndex * 0.5,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              {figure}
            </motion.div>
          ))}
        </div>

        {/* Portal Glow Effect */}
        <motion.div
          className="absolute inset-0 rounded-2xl opacity-30"
          animate={{
            boxShadow: [
              '0 0 20px rgba(59, 130, 246, 0.3)',
              '0 0 40px rgba(59, 130, 246, 0.6)',
              '0 0 20px rgba(59, 130, 246, 0.3)',
            ],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Content */}
        <div className="relative z-10 flex flex-col justify-center h-full">
          <div className="text-center">
            <motion.div
              layoutId={`category-icon-${category.id}`}
              className="text-4xl mb-4"
              animate={iconAnimation}
              transition={iconTransition}
              onAnimationStart={() => {
                console.log(`Animation started for ${category.id}, isThisCategoryTransitioning: ${isThisCategoryTransitioning}`)
              }}
              onAnimationComplete={() => {
                console.log(`Animation completed for ${category.id}, isThisCategoryTransitioning: ${isThisCategoryTransitioning}`)
              }}
            >
              {category.icon}
            </motion.div>
            <motion.h3 
              layoutId={`category-title-${category.id}`}
              className="text-xl font-bold text-gray-900 dark:text-white mb-2"
              transition={{ duration: 0.6, ease: "easeInOut" }}
            >
              {category.name}
            </motion.h3>
            <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
              {category.description}
            </p>
          </div>
        </div>

        {/* Portal Edge Blend */}
        <div className="absolute inset-0 rounded-2xl border-2 border-white/30 pointer-events-none" />
      </motion.div>
    </motion.div>
  )
}