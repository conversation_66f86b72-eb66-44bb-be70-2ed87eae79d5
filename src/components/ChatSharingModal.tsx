'use client'

import { useState, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  X, 
  Download, 
  Share2, 
  Upload, 
  Copy, 
  Check, 
  FileText, 
  Link, 
  AlertCircle,
  Settings
} from 'lucide-react'
import { useChatSharing } from '@/hooks/useChatSharing'
import { ShareOptions, ImportResult } from '@/types/chat-sharing'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

interface ChatSharingModalProps {
  isOpen: boolean
  onClose: () => void
  mvpId: string
  mvpName: string
  messages: Message[]
  onImportChat: (messages: Message[]) => void
}

export function ChatSharingModal({ 
  isOpen, 
  onClose, 
  mvpId, 
  mvpName, 
  messages, 
  onImportChat 
}: ChatSharingModalProps) {
  const [activeTab, setActiveTab] = useState<'export' | 'import'>('export')
  const [shareOptions, setShareOptions] = useState<ShareOptions>({
    includePersonalInfo: true,
    allowContinuation: true,
    addNote: ''
  })
  const [copied, setCopied] = useState(false)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const {
    isExporting,
    isImporting,
    shareLink,
    downloadChatAsJSON,
    generateShareableURL,
    importChatFromFile,
    copyToClipboard,
    clearShareLink
  } = useChatSharing()

  const handleDownloadJSON = async () => {
    await downloadChatAsJSON(mvpId, messages, shareOptions)
  }

  const handleGenerateShareLink = async () => {
    const url = await generateShareableURL(mvpId, messages, shareOptions)
    if (url) {
      // Auto-copy to clipboard
      const success = await copyToClipboard(url)
      if (success) {
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      }
    }
  }

  const handleCopyLink = async () => {
    if (shareLink) {
      const success = await copyToClipboard(shareLink)
      if (success) {
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      }
    }
  }

  const handleFileImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const result = await importChatFromFile(file, onImportChat)
    setImportResult(result)
    
    if (result.success) {
      setTimeout(() => {
        onClose()
        setImportResult(null)
      }, 2000)
    }
  }

  const handleClose = () => {
    onClose()
    clearShareLink()
    setImportResult(null)
    setCopied(false)
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
        onClick={handleClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white dark:bg-gray-800 rounded-2xl p-6 max-w-md w-full max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Share Chat with {mvpName}
            </h2>
            <button
              onClick={handleClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <X size={24} />
            </button>
          </div>

          {/* Tabs */}
          <div className="flex mb-6 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setActiveTab('export')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'export'
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              Export & Share
            </button>
            <button
              onClick={() => setActiveTab('import')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'import'
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              Import Chat
            </button>
          </div>

          {/* Export Tab */}
          {activeTab === 'export' && (
            <div className="space-y-4">
              {/* Chat Info */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <FileText size={16} className="text-gray-600 dark:text-gray-400" />
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    Chat Summary
                  </span>
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  {messages.length} messages • Started {messages[0]?.timestamp.toLocaleDateString()}
                </div>
              </div>

              {/* Share Options */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Settings size={16} className="text-gray-600 dark:text-gray-400" />
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    Share Options
                  </span>
                </div>
                
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={shareOptions.allowContinuation}
                    onChange={(e) => setShareOptions(prev => ({ 
                      ...prev, 
                      allowContinuation: e.target.checked 
                    }))}
                    className="rounded border-gray-300 dark:border-gray-600"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    Allow others to continue this conversation
                  </span>
                </label>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Add a note (optional)
                  </label>
                  <textarea
                    value={shareOptions.addNote || ''}
                    onChange={(e) => setShareOptions(prev => ({ 
                      ...prev, 
                      addNote: e.target.value 
                    }))}
                    placeholder="Add context or instructions for the shared chat..."
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm resize-none"
                    rows={2}
                  />
                </div>
              </div>

              {/* Export Actions */}
              <div className="space-y-3">
                {/* Download JSON */}
                <button
                  onClick={handleDownloadJSON}
                  disabled={isExporting}
                  className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded-lg transition-colors"
                >
                  <Download size={18} />
                  {isExporting ? 'Exporting...' : 'Download as JSON'}
                </button>

                {/* Generate Share Link */}
                <button
                  onClick={handleGenerateShareLink}
                  disabled={isExporting}
                  className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white rounded-lg transition-colors"
                >
                  <Share2 size={18} />
                  {isExporting ? 'Generating...' : 'Generate Share Link'}
                </button>

                {/* Share Link Result */}
                {shareLink && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4"
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <Link size={16} className="text-green-600 dark:text-green-400" />
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        Share Link Generated
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <input
                        type="text"
                        value={shareLink}
                        readOnly
                        className="flex-1 px-3 py-2 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded text-sm text-gray-700 dark:text-gray-300"
                      />
                      <button
                        onClick={handleCopyLink}
                        className="px-3 py-2 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 rounded transition-colors"
                      >
                        {copied ? <Check size={16} /> : <Copy size={16} />}
                      </button>
                    </div>
                    {copied && (
                      <div className="mt-2 text-sm text-green-600 dark:text-green-400">
                        Link copied to clipboard!
                      </div>
                    )}
                  </motion.div>
                )}
              </div>
            </div>
          )}

          {/* Import Tab */}
          {activeTab === 'import' && (
            <div className="space-y-4">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Import a previously exported chat to continue the conversation.
              </div>

              {/* File Upload */}
              <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                <Upload size={32} className="mx-auto mb-4 text-gray-400" />
                <button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isImporting}
                  className="px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded-lg transition-colors"
                >
                  {isImporting ? 'Importing...' : 'Choose JSON File'}
                </button>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".json"
                  onChange={handleFileImport}
                  className="hidden"
                />
                <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  Select a chat export file (.json)
                </div>
              </div>

              {/* Import Result */}
              {importResult && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`rounded-lg p-4 ${
                    importResult.success 
                      ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' 
                      : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
                  }`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    {importResult.success ? (
                      <Check size={16} className="text-green-600 dark:text-green-400" />
                    ) : (
                      <AlertCircle size={16} className="text-red-600 dark:text-red-400" />
                    )}
                    <span className={`text-sm font-medium ${
                      importResult.success 
                        ? 'text-green-800 dark:text-green-200' 
                        : 'text-red-800 dark:text-red-200'
                    }`}>
                      {importResult.success ? 'Import Successful!' : 'Import Failed'}
                    </span>
                  </div>
                  
                  <div className={`text-sm ${
                    importResult.success 
                      ? 'text-green-700 dark:text-green-300' 
                      : 'text-red-700 dark:text-red-300'
                  }`}>
                    {importResult.success 
                      ? `Imported ${importResult.importedMessageCount} messages. Chat will be restored.`
                      : importResult.error
                    }
                  </div>

                  {importResult.warnings && importResult.warnings.length > 0 && (
                    <div className="mt-2 text-sm text-orange-600 dark:text-orange-400">
                      Warnings: {importResult.warnings.join(', ')}
                    </div>
                  )}
                </motion.div>
              )}
            </div>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
} 