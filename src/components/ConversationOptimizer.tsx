'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { TrendingDown, Info, X } from 'lucide-react'
import { getOptimizationStats } from '@/utils/conversation-optimizer'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

interface ConversationOptimizerProps {
  messages: Message[]
  show?: boolean
}

export function ConversationOptimizer({ messages, show = false }: ConversationOptimizerProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  
  if (messages.length < 8) return null // Only show for longer conversations
  
  const stats = getOptimizationStats(messages, 6)
  const { tokenSavings } = stats
  
  if (tokenSavings.savings <= 0) return null // No savings to show
  
  return (
    <AnimatePresence>
      {show && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          className="fixed bottom-20 right-4 z-40"
        >
          <motion.div
            className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg shadow-lg max-w-sm"
            layout
          >
            {/* Collapsed View */}
            {!isExpanded && (
              <motion.div
                className="p-3 cursor-pointer"
                onClick={() => setIsExpanded(true)}
                whileHover={{ scale: 1.02 }}
              >
                <div className="flex items-center gap-2">
                  <TrendingDown size={16} className="text-green-600 dark:text-green-400" />
                  <span className="text-sm font-medium text-green-800 dark:text-green-200">
                    {tokenSavings.savingsPercentage.toFixed(0)}% token savings
                  </span>
                  <Info size={14} className="text-green-600 dark:text-green-400 ml-auto" />
                </div>
              </motion.div>
            )}
            
            {/* Expanded View */}
            {isExpanded && (
              <motion.div
                initial={{ height: 'auto' }}
                className="p-4"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <TrendingDown size={16} className="text-green-600 dark:text-green-400" />
                    <span className="text-sm font-medium text-green-800 dark:text-green-200">
                      Conversation Optimized
                    </span>
                  </div>
                  <button
                    onClick={() => setIsExpanded(false)}
                    className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200"
                  >
                    <X size={16} />
                  </button>
                </div>
                
                <div className="space-y-2 text-sm text-green-700 dark:text-green-300">
                  <div className="flex justify-between">
                    <span>Total messages:</span>
                    <span>{stats.totalMessages}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Optimized messages:</span>
                    <span>{stats.dualLanguageMessagesOptimized}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Token savings:</span>
                    <span>{tokenSavings.savings} (~{tokenSavings.savingsPercentage.toFixed(1)}%)</span>
                  </div>
                  <div className="flex justify-between font-medium">
                    <span>Estimated tokens:</span>
                    <span>{tokenSavings.optimizedApproxTokens}</span>
                  </div>
                </div>
                
                <div className="mt-3 pt-3 border-t border-green-200 dark:border-green-800">
                  <p className="text-xs text-green-600 dark:text-green-400">
                    Older dual-language messages converted to English-only to reduce costs while maintaining context.
                  </p>
                </div>
              </motion.div>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
} 