'use client'

import { motion } from 'framer-motion'
import { useEffect, useState } from 'react'

export function UsageCounter() {
  const [remainingMessages, setRemainingMessages] = useState(50)
  const [isLoading, setIsLoading] = useState(true)

  const updateRemainingMessages = () => {
    const today = new Date().toDateString()
    const stored = localStorage.getItem('vox-vitae-usage')
    
    if (stored) {
      const usage = JSON.parse(stored)
      if (usage.date === today) {
        const remaining = Math.max(0, 50 - usage.count)
        setRemainingMessages(remaining)
        console.log('Usage counter updated:', { count: usage.count, remaining })
      } else {
        // Reset for new day
        localStorage.setItem('vox-vitae-usage', JSON.stringify({ date: today, count: 0 }))
        setRemainingMessages(50)
        console.log('New day reset, remaining messages: 50')
      }
    } else {
      // First time user
      localStorage.setItem('vox-vitae-usage', JSON.stringify({ date: today, count: 0 }))
      setRemainingMessages(50)
      console.log('First time user, remaining messages: 50')
    }
  }

  useEffect(() => {
    // Initial load
    updateRemainingMessages()
    setIsLoading(false)

    // Listen for storage changes (when messages are sent)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'vox-vitae-usage') {
        updateRemainingMessages()
      }
    }

    // Listen for custom events (for same-tab updates)
    const handleUsageUpdate = () => {
      updateRemainingMessages()
    }

    window.addEventListener('storage', handleStorageChange)
    window.addEventListener('usage-updated', handleUsageUpdate)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      window.removeEventListener('usage-updated', handleUsageUpdate)
    }
  }, [])

  if (isLoading) {
    return (
      <div className="text-center">
        <div className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.8, duration: 0.6 }}
      className="text-center"
    >
      <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 shadow-lg border border-white/20">
        <motion.div
          animate={{ scale: [1, 1.1, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="text-xl"
        >
          💬
        </motion.div>
        <span className="text-sm font-medium text-gray-700">
          {remainingMessages} messages remaining today
        </span>
      </div>
    </motion.div>
  )
}