'use client'

import { motion } from 'framer-motion'
import { MVP, isEnglishNative } from '@/data/mvps'
import { Category } from '@/data/categories'
import { DualLanguageMessage } from './DualLanguageMessage'
import { SimpleTextMessage } from './SimpleTextMessage'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

interface ChatMessageProps {
  message: Message
  mvp: MVP
  category: Category
  isStreaming?: boolean
}

export function ChatMessage({ message, mvp, category, isStreaming = false }: ChatMessageProps) {
  const isUser = message.role === 'user'

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.4, ease: "easeOut" }}
      className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}
    >
      <div className={`flex items-start gap-3 max-w-3xl ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
        {/* Avatar */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.1, type: "spring", stiffness: 300 }}
          className={`
            flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center text-lg
            ${isUser 
              ? 'bg-blue-500 text-white' 
              : `bg-gradient-to-r ${category.color} text-white`
            }
          `}
        >
          {isUser ? '👤' : mvp.avatar}
        </motion.div>

        {/* Message Bubble */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.3 }}
          className={`
            relative px-4 py-3 rounded-2xl max-w-xl
            ${isUser 
              ? 'bg-blue-500 text-white rounded-br-sm' 
              : 'bg-gray-800 text-gray-200 rounded-bl-sm shadow-md border border-gray-600'
            }
          `}
        >
          {isUser ? (
            <p className="text-sm leading-relaxed whitespace-pre-wrap">
              {message.content}
            </p>
          ) : isEnglishNative(mvp) ? (
            <SimpleTextMessage 
              content={message.content}
              isStreaming={isStreaming}
            />
          ) : (
            <DualLanguageMessage 
              content={message.content}
              mvp={mvp}
              isStreaming={isStreaming}
            />
          )}
          
          {/* Message tail */}
          <div className={`
            absolute top-0 w-0 h-0
            ${isUser 
              ? 'right-0 border-l-8 border-l-blue-500 border-t-8 border-t-transparent' 
              : 'left-0 border-r-8 border-r-gray-800 border-t-8 border-t-transparent'
            }
          `} />
        </motion.div>
      </div>
    </motion.div>
  )
}