import { <PERSON> } from '@/data/mvps'

export async function sendMessageToAI(
  mvp: <PERSON>, 
  messages: Array<{role: 'user' | 'assistant', content: string}>,
  onChunk?: (chunk: string) => void
): Promise<string> {
  try {
    const response = await fetch('/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ mvp, messages }),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const reader = response.body?.getReader()
    const decoder = new TextDecoder()
    let fullContent = ''

    if (!reader) {
      throw new Error('No reader available')
    }

    while (true) {
      const { done, value } = await reader.read()
      if (done) {
        break
      }

      const chunk = decoder.decode(value)
      const lines = chunk.split('\n')
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6)
          if (data === '[DONE]') {
            return fullContent
          }
          
          try {
            const parsed = JSON.parse(data)
            if (parsed.content) {
              fullContent += parsed.content
              onChunk?.(parsed.content)
            }
          } catch {
            // Invalid JSON in chunk, skip
          }
        }
      }
    }

    return fullContent
  } catch {
    throw new Error('Failed to get response from AI')
  }
}