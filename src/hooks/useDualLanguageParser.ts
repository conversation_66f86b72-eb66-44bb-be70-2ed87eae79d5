/**
 * useDualLanguageParser Hook
 * React hook for managing dual language parsing with streaming support
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { DualLanguageParser } from '@/utils/dual-language-parser'
import { SentenceTracker } from '@/utils/sentence-tracker'
import { 
  StreamingMessage, 
  ParserConfig, 
  SentenceEvent, 
  UseDualLanguageParser 
} from '@/types/dual-language'
import { MVP, getMVPLanguage } from '@/data/mvps'

const defaultConfig: ParserConfig = {
  translationDelay: 2000,
  maxParseAttempts: 3,
  enableDebugMode: false,
  fallbackToOriginal: true,
  sentenceMinLength: 3,
  translationTimeout: 500
}

export function useDualLanguageParser(
  mvp: MVP,
  config: Partial<ParserConfig> = {},
  onSentenceEvent?: (event: SentenceEvent) => void,
  onParsingError?: (error: string) => void
): UseDualLanguageParser {
  const mergedConfig = { ...defaultConfig, ...config }
  
  // Initialize parser and tracker
  const parserRef = useRef<DualLanguageParser>()
  const trackerRef = useRef<SentenceTracker>()
  
  if (!parserRef.current) {
    parserRef.current = new DualLanguageParser(mergedConfig)
  }
  
  if (!trackerRef.current) {
    trackerRef.current = new SentenceTracker({
      translationDelay: mergedConfig.translationDelay,
      translationTimeout: mergedConfig.translationTimeout,
      enableDebugMode: mergedConfig.enableDebugMode
    })
  }

  // State for streaming message
  const [streamingMessage, setStreamingMessage] = useState<StreamingMessage>(() => {
    const language = getMVPLanguage(mvp)
    return {
      id: `msg-${Date.now()}`,
      content: '',
      mvpId: mvp.id,
      language: {
        ...language,
        punctuationPattern: /[.!?;:]/g,
        sentenceEndPattern: /[.!?;:]\s+/g
      },
      phase: 'waiting',
      parsingState: 'idle',
      parsedContent: {
        originalText: '',
        englishText: '',
        isOriginalComplete: false,
        isEnglishComplete: false,
        hasValidFormat: false,
        parseErrors: []
      },
      sentences: [],
      lastUpdated: Date.now()
    }
  })

  // Setup event listeners
  useEffect(() => {
    const tracker = trackerRef.current
    if (!tracker) return

    const handleSentenceEvent = (event: SentenceEvent) => {
      if (mergedConfig.enableDebugMode) {
        console.log('Sentence event:', event)
      }
      
      // Update streaming message with new sentence states
      setStreamingMessage(prev => ({
        ...prev,
        sentences: tracker.getSentences(),
        lastUpdated: Date.now()
      }))
      
      // Call external event handler
      onSentenceEvent?.(event)
    }

    tracker.addEventListener(handleSentenceEvent)
    
    return () => {
      tracker.removeEventListener(handleSentenceEvent)
    }
  }, [mergedConfig.enableDebugMode, onSentenceEvent])

  // Update content function
  const updateContent = useCallback((content: string) => {
    const parser = parserRef.current
    const tracker = trackerRef.current
    if (!parser || !tracker) {
      return
    }

    try {
      // Update parsing state
      setStreamingMessage(prev => ({
        ...prev,
        content,
        phase: 'streaming',
        parsingState: 'parsing_original',
        lastUpdated: Date.now()
      }))

      // Parse content with streaming flag
      const parseResult = parser.parse(content, mvp.id, true)
      
      if (!parseResult.success) {
        // Handle parsing errors
        const errorMsg = parseResult.errors.join(', ')
        onParsingError?.(errorMsg)
        
        setStreamingMessage(prev => ({
          ...prev,
          parsingState: 'error',
          parsedContent: {
            ...parseResult.content,
            parseErrors: parseResult.errors
          },
          debugInfo: mergedConfig.enableDebugMode ? {
            rawContent: content,
            parseAttempts: 1,
            errors: parseResult.errors,
            timings: { parseTime: Date.now() }
          } : undefined
        }))
        return
      }

      // Update tracker with new sentences
      tracker.updateSentences(parseResult.sentences)
      
      // Update streaming message
      setStreamingMessage(prev => ({
        ...prev,
        parsingState: parseResult.content.isEnglishComplete ? 'complete' : 'parsing_english',
        parsedContent: parseResult.content,
        sentences: tracker.getSentences(),
        phase: 'streaming',
        debugInfo: mergedConfig.enableDebugMode ? {
          rawContent: content,
          parseAttempts: 1,
          errors: parseResult.errors,
          timings: { parseTime: Date.now() }
        } : undefined
      }))

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown parsing error'
      onParsingError?.(errorMsg)
      
      setStreamingMessage(prev => ({
        ...prev,
        parsingState: 'error',
        parsedContent: {
          ...prev.parsedContent,
          parseErrors: [errorMsg]
        }
      }))
    }
  }, [mvp.id, mergedConfig.enableDebugMode, onParsingError])

  // Set streaming complete
  const setStreamingComplete = useCallback(() => {
    const tracker = trackerRef.current
    if (!tracker) return

    setStreamingMessage(prev => {
      // Mark all incomplete sentences as complete
      const updatedSentences = prev.sentences.map(sentence => {
        if (!sentence.isComplete) {
          tracker.markSentenceComplete(sentence.id)
          return { ...sentence, isComplete: true, state: 'complete' as const }
        }
        return sentence
      })

      return {
        ...prev,
        phase: 'complete',
        parsingState: 'complete',
        sentences: updatedSentences,
        lastUpdated: Date.now()
      }
    })
  }, [])

  // Reset function
  const reset = useCallback(() => {
    const tracker = trackerRef.current
    if (!tracker) return

    tracker.clear()
    
    const language = getMVPLanguage(mvp)
    setStreamingMessage({
      id: `msg-${Date.now()}`,
      content: '',
      mvpId: mvp.id,
      language: {
        ...language,
        punctuationPattern: /[.!?;:]/g,
        sentenceEndPattern: /[.!?;:]\s+/g
      },
      phase: 'waiting',
      parsingState: 'idle',
      parsedContent: {
        originalText: '',
        englishText: '',
        isOriginalComplete: false,
        isEnglishComplete: false,
        hasValidFormat: false,
        parseErrors: []
      },
      sentences: [],
      lastUpdated: Date.now()
    })
  }, [mvp])

  // Get debug info
  const getDebugInfo = useCallback(() => {
    const parser = parserRef.current
    const tracker = trackerRef.current
    
    return {
      parser: parser?.getConfig(),
      tracker: tracker?.getDebugInfo(),
      streamingMessage: {
        phase: streamingMessage.phase,
        parsingState: streamingMessage.parsingState,
        sentenceCount: streamingMessage.sentences.length,
        hasValidFormat: streamingMessage.parsedContent.hasValidFormat,
        errors: streamingMessage.parsedContent.parseErrors
      }
    }
  }, [streamingMessage])

  return {
    streamingMessage,
    updateContent,
    setStreamingComplete,
    reset,
    getDebugInfo
  }
} 