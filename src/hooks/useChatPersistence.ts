'use client'

import { useState, useEffect } from 'react'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

interface ChatData {
  messages: Message[]
  lastUpdated: Date
}

const STORAGE_KEY_PREFIX = 'vox_vitae_chat_'
const MAX_STORAGE_AGE_DAYS = 30

export function useChatPersistence(mvpId: string) {
  const [messages, setMessages] = useState<Message[]>([])
  const [isLoaded, setIsLoaded] = useState(false)
  const storageKey = `${STORAGE_KEY_PREFIX}${mvpId}`

  // Load messages from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(storageKey)
      if (stored) {
        const chatData: ChatData = JSON.parse(stored)
        
        // Check if data is not too old
        const lastUpdated = new Date(chatData.lastUpdated)
        const daysSinceUpdate = (Date.now() - lastUpdated.getTime()) / (1000 * 60 * 60 * 24)
        
        if (daysSinceUpdate <= MAX_STORAGE_AGE_DAYS) {
          // Restore dates from stored strings
          const restoredMessages = chatData.messages.map(msg => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }))
          setMessages(restoredMessages)
        } else {
          // Clear old data
          localStorage.removeItem(storageKey)
        }
      }
    } catch (error) {
      console.error('Error loading chat from localStorage:', error)
      localStorage.removeItem(storageKey)
    }
    setIsLoaded(true)
  }, [mvpId, storageKey])

  // Save messages to localStorage whenever messages change
  useEffect(() => {
    if (messages.length > 0) {
      try {
        const chatData: ChatData = {
          messages,
          lastUpdated: new Date()
        }
        localStorage.setItem(storageKey, JSON.stringify(chatData))
      } catch (error) {
        console.error('Error saving chat to localStorage:', error)
        // If storage is full, try to clear old chats
        clearOldChats()
      }
    }
  }, [messages, storageKey])

  // Clear old chats to free up space
  const clearOldChats = () => {
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith(STORAGE_KEY_PREFIX))
      
      keys.forEach(key => {
        try {
          const stored = localStorage.getItem(key)
          if (stored) {
            const chatData: ChatData = JSON.parse(stored)
            const lastUpdated = new Date(chatData.lastUpdated)
            const daysSinceUpdate = (Date.now() - lastUpdated.getTime()) / (1000 * 60 * 60 * 24)
            
            if (daysSinceUpdate > MAX_STORAGE_AGE_DAYS) {
              localStorage.removeItem(key)
            }
          }
        } catch {
          localStorage.removeItem(key)
        }
      })
    } catch (error) {
      console.error('Error clearing old chats:', error)
    }
  }

  // Check if a user message already exists (to prevent duplicate API calls)
  const isDuplicateMessage = (content: string): boolean => {
    return messages.some(msg => 
      msg.role === 'user' && 
      msg.content.toLowerCase().trim() === content.toLowerCase().trim()
    )
  }

  // Get the assistant's response to a specific user message
  const getExistingResponse = (userContent: string): string | null => {
    const userMessageIndex = messages.findIndex(msg => 
      msg.role === 'user' && 
      msg.content.toLowerCase().trim() === userContent.toLowerCase().trim()
    )
    
    if (userMessageIndex !== -1 && userMessageIndex < messages.length - 1) {
      const nextMessage = messages[userMessageIndex + 1]
      if (nextMessage.role === 'assistant') {
        return nextMessage.content
      }
    }
    
    return null
  }

  // Clear chat for this MVP
  const clearChat = () => {
    setMessages([])
    localStorage.removeItem(storageKey)
  }

  // Clear all chats
  const clearAllChats = () => {
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith(STORAGE_KEY_PREFIX))
      keys.forEach(key => localStorage.removeItem(key))
    } catch (error) {
      console.error('Error clearing all chats:', error)
    }
  }

  return {
    messages,
    setMessages,
    isDuplicateMessage,
    getExistingResponse,
    clearChat,
    clearAllChats,
    isLoaded
  }
}