/**
 * useSentenceTracker Hook
 * React hook for managing sentence tracking and translation states
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { SentenceTracker } from '@/utils/sentence-tracker'
import { 
  SentenceData, 
  SentenceEvent, 
  UseSentenceTracker 
} from '@/types/dual-language'

export function useSentenceTracker(
  config: {
    translationDelay?: number
    translationTimeout?: number
    enableDebugMode?: boolean
  } = {},
  onSentenceEvent?: (event: SentenceEvent) => void
): UseSentenceTracker {
  const defaultConfig = {
    translationDelay: 2000,
    translationTimeout: 500,
    enableDebugMode: false,
    ...config
  }

  // Initialize tracker
  const trackerRef = useRef<SentenceTracker>()
  
  if (!trackerRef.current) {
    trackerRef.current = new SentenceTracker(defaultConfig)
  }

  // State for sentences
  const [sentences, setSentences] = useState<SentenceData[]>([])

  // Setup event listeners
  useEffect(() => {
    const tracker = trackerRef.current
    if (!tracker) return

    const handleSentenceEvent = (event: SentenceEvent) => {
      if (defaultConfig.enableDebugMode) {
        console.log('Sentence tracker event:', event)
      }
      
      // Update sentences state
      setSentences(tracker.getSentences())
      
      // Call external event handler
      onSentenceEvent?.(event)
    }

    tracker.addEventListener(handleSentenceEvent)
    
    return () => {
      tracker.removeEventListener(handleSentenceEvent)
    }
  }, [defaultConfig.enableDebugMode, onSentenceEvent])

  // Update sentences function
  const updateSentences = useCallback((newSentences: SentenceData[]) => {
    const tracker = trackerRef.current
    if (!tracker) return

    tracker.updateSentences(newSentences)
    setSentences(tracker.getSentences())
  }, [])

  // Mark sentence complete
  const markSentenceComplete = useCallback((sentenceId: string) => {
    const tracker = trackerRef.current
    if (!tracker) return

    tracker.markSentenceComplete(sentenceId)
    setSentences(tracker.getSentences())
  }, [])

  // Start translation
  const startTranslation = useCallback((sentenceId: string) => {
    const tracker = trackerRef.current
    if (!tracker) return

    tracker.startTranslation(sentenceId)
    setSentences(tracker.getSentences())
  }, [])

  // Complete translation
  const completeTranslation = useCallback((sentenceId: string) => {
    const tracker = trackerRef.current
    if (!tracker) return

    tracker.completeTranslation(sentenceId)
    setSentences(tracker.getSentences())
  }, [])

  // Get active sentences
  const getActiveSentences = useCallback((): SentenceData[] => {
    const tracker = trackerRef.current
    if (!tracker) return []

    return tracker.getActiveSentences()
  }, [])

  // Get pending translations
  const getPendingTranslations = useCallback((): SentenceData[] => {
    const tracker = trackerRef.current
    if (!tracker) return []

    return tracker.getPendingTranslations()
  }, [])

  return {
    sentences,
    updateSentences,
    markSentenceComplete,
    startTranslation,
    completeTranslation,
    getActiveSentences,
    getPendingTranslations
  }
} 