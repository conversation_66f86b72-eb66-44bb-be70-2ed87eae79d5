'use client'

import { useState, useEffect } from 'react'

const DAILY_LIMIT = 50

export function useRateLimit() {
  const [remainingMessages, setRemainingMessages] = useState(DAILY_LIMIT)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const today = new Date().toDateString()
    const stored = localStorage.getItem('vox-vitae-usage')
    
    if (stored) {
      const usage = JSON.parse(stored)
      if (usage.date === today) {
        setRemainingMessages(Math.max(0, DAILY_LIMIT - usage.count))
      } else {
        // Reset for new day
        localStorage.setItem('vox-vitae-usage', JSON.stringify({ date: today, count: 0 }))
        setRemainingMessages(DAILY_LIMIT)
      }
    } else {
      // First time user
      localStorage.setItem('vox-vitae-usage', JSON.stringify({ date: today, count: 0 }))
      setRemainingMessages(DAILY_LIMIT)
    }
    
    setIsLoading(false)
  }, [])

  const incrementUsage = () => {
    const today = new Date().toDateString()
    const stored = localStorage.getItem('vox-vitae-usage')
    
    if (stored) {
      const usage = JSON.parse(stored)
      const newCount = usage.count + 1
      localStorage.setItem('vox-vitae-usage', JSON.stringify({ date: today, count: newCount }))
      setRemainingMessages(Math.max(0, DAILY_LIMIT - newCount))
      
      // Dispatch custom event to update usage counter
      window.dispatchEvent(new CustomEvent('usage-updated'))
    }
  }

  const canSendMessage = remainingMessages > 0

  return {
    remainingMessages,
    canSendMessage,
    incrementUsage,
    isLoading
  }
}