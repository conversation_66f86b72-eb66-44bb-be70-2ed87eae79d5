'use client'

import { useState, useCallback } from 'react'
import { getMVPById } from '@/data/mvps'
import { 
  ExportedChat, 
  ImportResult, 
  ShareOptions,
  Message 
} from '@/types/chat-sharing'

const CHAT_SHARING_VERSION = '1.0.0'

export function useChatSharing() {
  const [isExporting, setIsExporting] = useState(false)
  const [isImporting, setIsImporting] = useState(false)
  const [shareLink, setShareLink] = useState<string | null>(null)

  // Export chat to shareable format
  const exportChat = useCallback(async (
    mvpId: string, 
    messages: Message[], 
    options: ShareOptions = {}
  ): Promise<ExportedChat | null> => {
    try {
      setIsExporting(true)
      
      const mvp = getMVPById(mvpId)
      if (!mvp) {
        throw new Error('MVP not found')
      }

      // Filter messages if needed
      let exportMessages = messages
      if (!options.includePersonalInfo) {
        // Could add logic to filter out personal information
        exportMessages = messages.map(msg => ({
          ...msg,
          content: msg.content // Could sanitize content here
        }))
      }

      const userMessages = exportMessages.filter(m => m.role === 'user')
      const assistantMessages = exportMessages.filter(m => m.role === 'assistant')
      
      const exportedChat: ExportedChat = {
        id: `chat_${Date.now()}_${mvpId}`,
        mvpId,
        mvpName: mvp.name,
        exportedAt: new Date(),
        version: CHAT_SHARING_VERSION,
        messages: exportMessages,
        messageCount: exportMessages.length,
        lastUpdated: new Date(),
        sessionInfo: {
          startedAt: exportMessages[0]?.timestamp || new Date(),
          totalMessages: exportMessages.length,
          userMessageCount: userMessages.length,
          assistantMessageCount: assistantMessages.length
        },
        shareInfo: {
          shareNote: options.addNote,
          isPublic: options.generatePublicLink,
          allowContinuation: options.allowContinuation !== false
        }
      }

      return exportedChat
    } catch (error) {
      console.error('Error exporting chat:', error)
      return null
    } finally {
      setIsExporting(false)
    }
  }, [])

  // Import chat from exported format
  const importChat = useCallback(async (
    exportedChat: ExportedChat,
    setMessages: (messages: Message[]) => void
  ): Promise<ImportResult> => {
    try {
      setIsImporting(true)
      
      // Validate the exported chat
      if (!exportedChat.mvpId || !exportedChat.messages) {
        return {
          success: false,
          error: 'Invalid chat data format'
        }
      }

      // Check if MVP exists
      const mvp = getMVPById(exportedChat.mvpId)
      if (!mvp) {
        return {
          success: false,
          error: `Historical figure "${exportedChat.mvpName}" not found`
        }
      }

      // Validate messages
      const validMessages = exportedChat.messages.filter(msg => 
        msg.role && msg.content && msg.timestamp
      )

      if (validMessages.length === 0) {
        return {
          success: false,
          error: 'No valid messages found in chat'
        }
      }

      // Restore message timestamps
      const restoredMessages = validMessages.map(msg => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      }))

      // Set the messages (this will trigger the chat persistence)
      setMessages(restoredMessages)

      const warnings = []
      if (validMessages.length < exportedChat.messages.length) {
        warnings.push(`${exportedChat.messages.length - validMessages.length} invalid messages were skipped`)
      }

      return {
        success: true,
        chatId: exportedChat.id,
        importedMessageCount: validMessages.length,
        warnings
      }
    } catch (error) {
      console.error('Error importing chat:', error)
      return {
        success: false,
        error: 'Failed to import chat: ' + (error as Error).message
      }
    } finally {
      setIsImporting(false)
    }
  }, [])

  // Export chat as JSON file download
  const downloadChatAsJSON = useCallback(async (
    mvpId: string, 
    messages: Message[], 
    options: ShareOptions = {}
  ) => {
    const exportedChat = await exportChat(mvpId, messages, options)
    if (!exportedChat) return

    const dataStr = JSON.stringify(exportedChat, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = `chat_${exportedChat.mvpName}_${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }, [exportChat])

  // Import chat from JSON file
  const importChatFromFile = useCallback(async (
    file: File,
    setMessages: (messages: Message[]) => void
  ): Promise<ImportResult> => {
    try {
      const text = await file.text()
      const exportedChat = JSON.parse(text) as ExportedChat
      return await importChat(exportedChat, setMessages)
    } catch {
      return {
        success: false,
        error: 'Invalid JSON file or corrupted chat data'
      }
    }
  }, [importChat])

  // Generate shareable URL (using base64 encoding for simplicity)
  const generateShareableURL = useCallback(async (
    mvpId: string, 
    messages: Message[], 
    options: ShareOptions = {}
  ): Promise<string | null> => {
    try {
      const exportedChat = await exportChat(mvpId, messages, options)
      if (!exportedChat) return null

      // Encode chat data as base64 for URL sharing
      const chatData = JSON.stringify(exportedChat)
      const encodedData = btoa(encodeURIComponent(chatData))
      
      const baseUrl = window.location.origin
      const shareUrl = `${baseUrl}/chat/${mvpId}?import=${encodedData}`
      
      setShareLink(shareUrl)
      return shareUrl
    } catch (error) {
      console.error('Error generating shareable URL:', error)
      return null
    }
  }, [exportChat])

  // Copy to clipboard
  const copyToClipboard = useCallback(async (text: string): Promise<boolean> => {
    try {
      await navigator.clipboard.writeText(text)
      return true
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
      return false
    }
  }, [])

  // Parse import data from URL
  const parseImportFromURL = useCallback((): ExportedChat | null => {
    try {
      const urlParams = new URLSearchParams(window.location.search)
      const importData = urlParams.get('import')
      
      if (!importData) return null
      
      const decodedData = decodeURIComponent(atob(importData))
      const exportedChat = JSON.parse(decodedData) as ExportedChat
      
      return exportedChat
    } catch (error) {
      console.error('Error parsing import data from URL:', error)
      return null
    }
  }, [])

  return {
    // State
    isExporting,
    isImporting,
    shareLink,
    
    // Export functions
    exportChat,
    downloadChatAsJSON,
    generateShareableURL,
    
    // Import functions
    importChat,
    importChatFromFile,
    parseImportFromURL,
    
    // Utility functions
    copyToClipboard,
    
    // Reset state
    clearShareLink: () => setShareLink(null)
  }
} 