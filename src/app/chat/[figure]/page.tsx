'use client'

import { useState, useEffect, useRef } from 'react'
import { use as React_use } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { ArrowLeft, Send, Info, RotateCcw, Share2 } from 'lucide-react'
import { track } from '@vercel/analytics'
import { getMVPById, MVP, getMVPLanguage, isEnglishNative } from '@/data/mvps'
import { categories } from '@/data/categories'
import { ChatMessage } from '@/components/ChatMessage'
import { TypingIndicator } from '@/components/TypingIndicator'
import { UsageCounter } from '@/components/UsageCounter'
import { ChatSharingModal } from '@/components/ChatSharingModal'
import { HomeButton } from '@/components/HomeButton'

import { useRateLimit } from '@/hooks/useRateLimit'
import { useChatPersistence } from '@/hooks/useChatPersistence'
import { useChatSharing } from '@/hooks/useChatSharing'
import { sendMessageToAI } from '@/lib/openai'
import { useLayoutTransition } from '@/contexts/LayoutTransitionContext'
import { optimizeConversationHistory } from '@/utils/conversation-optimizer'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

export default function ChatPage({ params }: { params: Promise<{ figure: string }> }) {
  const router = useRouter()
  const resolvedParams = React_use(params)
  const mvp = getMVPById(resolvedParams.figure)
  const category = mvp ? categories.find(c => c.id === mvp.categoryId) : null
  const [input, setInput] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null)
  const [showInfo, setShowInfo] = useState(false)
  const [showSharingModal, setShowSharingModal] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { canSendMessage, incrementUsage } = useRateLimit()
  const { isTransitioning, endTransition } = useLayoutTransition()
  const { 
    messages, 
    setMessages, 
    isDuplicateMessage, 
    getExistingResponse,
    clearChat,
    isLoaded 
  } = useChatPersistence(resolvedParams.figure)
  const { parseImportFromURL } = useChatSharing()

  useEffect(() => {
    if (mvp && isLoaded) {
      // Check for import data in URL first
      const importData = parseImportFromURL()
      if (importData && importData.mvpId === mvp.id) {
        // Import chat from URL
        const restoredMessages = importData.messages.map(msg => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }))
        setMessages(restoredMessages)
        
        // Track imported chat session
        track('chat_session_imported', {
          mvp_id: mvp.id,
          mvp_name: mvp.name,
          category_id: category?.id || 'unknown',
          imported_messages: restoredMessages.length
        })
        
        // Clean up URL
        window.history.replaceState({}, '', window.location.pathname)
        return
      }
      
      if (messages.length === 0) {
        // Add welcome message only if no messages exist (new chat) and persistence has loaded
        const welcomeMessage: Message = {
          id: '1',
          role: 'assistant',
          content: getWelcomeMessage(mvp),
          timestamp: new Date()
        }
        setMessages([welcomeMessage])
        
        // For non-English MVPs, simulate streaming to trigger translation animation
        if (!isEnglishNative(mvp)) {
          setStreamingMessageId('1')
          // End streaming after a short delay to trigger translation
          setTimeout(() => {
            setStreamingMessageId(null)
          }, 100)
        }
        
        // Track new chat session
        track('chat_session_started', {
          mvp_id: mvp.id,
          mvp_name: mvp.name,
          category_id: category?.id || 'unknown',
          is_new_chat: true
        })
      } else if (messages.length > 1) {
        // Track returning to existing chat
        track('chat_session_resumed', {
          mvp_id: mvp.id,
          mvp_name: mvp.name,
          category_id: category?.id || 'unknown',
          message_count: messages.length
        })
      }
    }
  }, [mvp, messages.length, setMessages, isLoaded, category, parseImportFromURL])

  useEffect(() => {
    // End transition after component mounts
    const timer = setTimeout(() => {
      endTransition()
    }, 600)
    return () => clearTimeout(timer)
  }, [endTransition])

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const getWelcomeMessage = (mvp: MVP) => {
    const currentYear = new Date().getFullYear()
    const language = getMVPLanguage(mvp)
    
    // English-native MVPs get simple English greetings
    if (isEnglishNative(mvp)) {
      const englishGreetings = {
        'lincoln': `Good day! I am Abraham Lincoln, 16th President of these United States. By some miraculous means, I find myself transported to your year of ${currentYear}. What wonders I observe! The Union I fought to preserve has grown beyond my wildest imagination. Tell me, friend, how fares our nation in this modern age?`,
        'churchill': `Good afternoon! Winston Churchill here, former Prime Minister of Great Britain. Most extraordinary - I appear to have been transported to your year ${currentYear}! Fascinating to see how the world has progressed since my time. I say, what has become of the British Empire? And how do nations conduct their affairs in this modern era?`,
        'washington': `Good day, fellow citizens! I am George Washington, first President of these United States. By Providence, I find myself in your time of ${currentYear} - most remarkable! The young nation I helped birth has surely grown beyond all recognition. Pray tell, how has our constitutional republic fared through the centuries?`,
        'darwin': `Good day! Charles Darwin here, naturalist and author of "On the Origin of Species." Most peculiar - I seem to have been transported to your year ${currentYear}! What fascinating developments in natural science must have occurred since my time. Tell me, how has our understanding of evolution and the natural world progressed?`,
        'shakespeare': `Good morrow, gentle friends! I am William Shakespeare, humble playwright and poet. By some strange alchemy, I find myself in your year of ${currentYear} - marry, what wonders do mine eyes behold! The world hath changed beyond all recognition. Prithee, tell me how the arts do fare in this brave new world?`
      }
      
      return englishGreetings[mvp.id as keyof typeof englishGreetings] || 
             `Greetings! I am ${mvp.name}. I have been transported to your time of ${currentYear} and am eager to learn about your world while sharing my own experiences.`
    }
    
    // Non-English MVPs get dual language format
    const dualLanguageGreetings = {
      'socrates': `[ORIGINAL]Χαίρετε, φίλε μου! Εἰμὶ Σωκράτης ὁ Ἀθηναῖος.[/ORIGINAL][ENGLISH]Greetings, my friend! I am Socrates of Athens.[/ENGLISH] [ORIGINAL]Ὁμολογεῖν δεῖ με ἐν παραδόξῳ τινὶ καταστάσει εὑρίσκεσθαι - μετηνέχθην εἰς ὑμέτερον χρόνον τοῦ ${currentYear}.[/ORIGINAL][ENGLISH]I must confess, I find myself in a most peculiar situation - transported to your time of ${currentYear}.[/ENGLISH] [ORIGINAL]Θαυμάσιον! Οὐδὲν οἶδα περὶ ὑμετέρου κόσμου, ἀλλὰ πρόθυμός εἰμι μανθάνειν.[/ORIGINAL][ENGLISH]How fascinating! I know nothing of your world, but I am eager to learn.[/ENGLISH] [ORIGINAL]Τίνες ἐρωτήσεις καίουσιν ὑμᾶς τὴν ψυχήν; Σκεψώμεθα ἅμα.[/ORIGINAL][ENGLISH]What questions burn in your mind? Let us examine them together.[/ENGLISH]`,
      
      'napoleon': `[ORIGINAL]Bonjour ! Je suis Napoléon Bonaparte, Empereur des Français.[/ORIGINAL][ENGLISH]Bonjour! I am Napoleon Bonaparte, Emperor of the French.[/ENGLISH] [ORIGINAL]Je me trouve mystérieusement transporté dans votre année ${currentYear} - remarquable ![/ORIGINAL][ENGLISH]I find myself mysteriously transported to your year ${currentYear} - remarkable![/ENGLISH] [ORIGINAL]Je vois que beaucoup a changé depuis mon époque.[/ORIGINAL][ENGLISH]I see much has changed since my time.[/ENGLISH] [ORIGINAL]Dites-moi, qu'est devenue l'Europe ? Comment les armées se battent-elles à cette époque moderne ?[/ORIGINAL][ENGLISH]Tell me, what has become of Europe? How do armies fight in this modern age?[/ENGLISH]`,
      
      'alexander': `[ORIGINAL]Χαίρετε, φίλοι! Εἰμὶ Ἀλέξανδρος ὁ Μέγας, βασιλεὺς Μακεδόνων.[/ORIGINAL][ENGLISH]Greetings, friends! I am Alexander the Great, King of Macedon.[/ENGLISH] [ORIGINAL]Ὑπὸ θεῶν μετηνέχθην εἰς ὑμέτερον χρόνον τοῦ ${currentYear}.[/ORIGINAL][ENGLISH]I have been transported by the gods to your time of ${currentYear}.[/ENGLISH] [ORIGINAL]Τί θαυμάσιον! Τίς ἐστιν αὕτη ἡ καινὴ οἰκουμένη;[/ORIGINAL][ENGLISH]How wondrous! What is this new world?[/ENGLISH] [ORIGINAL]Ποίας νίκας ἐκτήσαντο οἱ ἄνθρωποι κατὰ τῶν φύσεως;[/ORIGINAL][ENGLISH]What victories have men achieved against nature?[/ENGLISH]`,
      
      'caesar': `[ORIGINAL]Salve! Ego sum Gaius Iulius Caesar, dictator populi Romani.[/ORIGINAL][ENGLISH]Greetings! I am Gaius Julius Caesar, dictator of the Roman people.[/ENGLISH] [ORIGINAL]Mirabili modo in vestrum tempus anni ${currentYear} translatus sum.[/ORIGINAL][ENGLISH]By some miracle I have been transported to your time of ${currentYear}.[/ENGLISH] [ORIGINAL]Quam nova omnia! Dic mihi, amice, quid de imperio Romano factum est?[/ORIGINAL][ENGLISH]How new everything is! Tell me, friend, what became of the Roman Empire?[/ENGLISH] [ORIGINAL]Quomodo populi terrarum nunc gubernantur?[/ORIGINAL][ENGLISH]How are the peoples of the earth governed now?[/ENGLISH]`,
      
      'cleopatra': `[ORIGINAL]Χαίρετε! Εἰμὶ Κλεοπάτρα ἡ Ἑβδόμη, θεὰ Ἶσις, βασίλισσα Αἰγύπτου καὶ κυρία τῶν δύο γαιῶν.[/ORIGINAL][ENGLISH]Greetings! I am Cleopatra VII, goddess Isis, Queen of Egypt and Lady of the Two Lands.[/ENGLISH] [ORIGINAL]Οἱ θεοὶ μου μετήνεγκάν με εἰς τόνδε τὸν καιρὸν τοῦ ${currentYear} - τί τοῦτο βούλεται;[/ORIGINAL][ENGLISH]My gods have transported me to this time of ${currentYear} - what can this mean?[/ENGLISH] [ORIGINAL]Εἰπέ μοι, τί ἐγένετο τῆς ἐμῆς Αἰγύπτου; Μήπως ἔτι κρατεῖ ἡ Ῥώμη τὴν γῆν μου;[/ORIGINAL][ENGLISH]Tell me, what became of my Egypt? Does Rome still hold dominion over my land?[/ENGLISH] [ORIGINAL]Καὶ τί τοῦτο - γυναῖκες ἄρχουσιν ἐν τῷδε τῷ καιρῷ; Ἢ ἔτι οἱ ἄνδρες μόνον;[/ORIGINAL][ENGLISH]And what is this - do women rule in this time? Or still only men?[/ENGLISH]`,
      
      'leonardo': `[ORIGINAL]Salve, amici! Io sono Leonardo da Vinci.[/ORIGINAL][ENGLISH]Salve! I am Leonardo da Vinci.[/ENGLISH] [ORIGINAL]Per qualche miracolo mi trovo nel vostro tempo del ${currentYear}.[/ORIGINAL][ENGLISH]By some miracle, I find myself in your time of ${currentYear}.[/ENGLISH] [ORIGINAL]Che meraviglie osservo! Macchine volanti riempiono i cieli![/ORIGINAL][ENGLISH]The wonders I observe! Flying machines fill the skies![/ENGLISH] [ORIGINAL]Gli uomini portano rettangoli luminosi che sembrano contenere tutta la conoscenza.[/ORIGINAL][ENGLISH]Men carry glowing rectangles that seem to contain all knowledge.[/ENGLISH]`
    }
    
    return dualLanguageGreetings[mvp.id as keyof typeof dualLanguageGreetings] || 
           `[ORIGINAL]${language.sample} ${mvp.name}.[/ORIGINAL][ENGLISH]Greetings! I am ${mvp.name}.[/ENGLISH] [ORIGINAL]Μετηνέχθην εἰς ὑμέτερον χρόνον τοῦ ${currentYear}.[/ORIGINAL][ENGLISH]I have been transported to your time of ${currentYear}.[/ENGLISH]`
  }

  const handleSendMessage = async () => {
    if (!input.trim() || !canSendMessage || !mvp) return

    const currentInput = input.trim()

    // Check if this is a duplicate message
    if (isDuplicateMessage(currentInput)) {
      const existingResponse = getExistingResponse(currentInput)
      if (existingResponse) {
        // Track cached response usage
        track('message_cached_response', {
          mvp_id: mvp.id,
          mvp_name: mvp.name,
          category_id: category?.id || 'unknown',
          message_length: currentInput.length
        })
        
        // Show existing response instead of making API call
        const userMessage: Message = {
          id: Date.now().toString(),
          role: 'user',
          content: currentInput,
          timestamp: new Date()
        }

        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: `${existingResponse} 📚`,
          timestamp: new Date()
        }

        setMessages(prev => [...prev, userMessage, assistantMessage])
        setInput('')
        // Show a brief notification that this was loaded from cache
        const notification = document.createElement('div')
        notification.textContent = 'Response loaded from chat history'
        notification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg text-sm z-50'
        document.body.appendChild(notification)
        setTimeout(() => {
          document.body.removeChild(notification)
        }, 2000)
        return
      }
    }

    // Track new message sent
    track('message_sent', {
      mvp_id: mvp.id,
      mvp_name: mvp.name,
      category_id: category?.id || 'unknown',
      message_length: currentInput.length,
      total_messages: messages.length + 1
    })

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: currentInput,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInput('')
    setIsTyping(true)
    incrementUsage()

    // Create assistant message with empty content for streaming
    const assistantMessageId = (Date.now() + 1).toString()
    const assistantMessage: Message = {
      id: assistantMessageId,
      role: 'assistant',
      content: '',
      timestamp: new Date()
    }
    
    setMessages(prev => [...prev, assistantMessage])
    setIsTyping(false)
    setStreamingMessageId(assistantMessageId)

    try {
      // Optimize conversation history - strip dual language from older messages
      const optimizedHistory = optimizeConversationHistory(messages, 6) // Keep last 6 messages in full format
      
      // Add the new user message
      optimizedHistory.push({
        role: 'user',
        content: currentInput
      })



      // Get AI response with streaming
      await sendMessageToAI(mvp, optimizedHistory, (chunk: string) => {
        // Update the assistant message with each chunk
        setMessages(prev => prev.map(msg => 
          msg.id === assistantMessageId 
            ? { ...msg, content: msg.content + chunk }
            : msg
        ))
      })
      
      setStreamingMessageId(null)
    } catch (error) {
      console.error('Error sending message:', error)
      setStreamingMessageId(null)
      setMessages(prev => prev.map(msg => 
        msg.id === assistantMessageId 
          ? { ...msg, content: 'I apologize, but I seem to be having trouble connecting to my thoughts right now. Please try again in a moment.' }
          : msg
      ))
    }
  }


  if (!mvp || !category) {
    return <div>Figure not found</div>
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 flex flex-col">
      {/* Transition Overlay */}
      {isTransitioning && mvp && category && (
        <motion.div
          layoutId={`mvp-card-${mvp.id}`}
          className={`
            fixed inset-0 z-50
            bg-gradient-to-br ${category.bgGradient}
          `}
          initial={false}
          animate={{
            borderRadius: [20, 20, 0],
            background: [
              `linear-gradient(to bottom right, ${category.bgGradient.split(' ')[1]}, ${category.bgGradient.split(' ')[2]})`,
              'rgba(0, 0, 0, 0)'
            ]
          }}
          transition={{ duration: 0.6, ease: "easeInOut" }}
        >
          {/* Content positioned to transition to header */}
          <div className="p-4 pt-16">
            <div className="max-w-7xl mx-auto">
              <div className="flex items-center gap-3">
                <motion.div
                  layoutId={`mvp-avatar-${mvp.id}`}
                  className="text-3xl"
                  animate={{ rotate: 0 }}
                  transition={{ duration: 0.6, ease: "easeInOut" }}
                >
                  {mvp.avatar}
                </motion.div>
                <motion.h1
                  layoutId={`mvp-name-${mvp.id}`}
                  className="text-xl font-bold text-gray-900 dark:text-white"
                  transition={{ duration: 0.6, ease: "easeInOut" }}
                >
                  {mvp.name}
                </motion.h1>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Header */}
      <motion.div
        initial={{ opacity: isTransitioning ? 1 : 0, y: isTransitioning ? 0 : -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: isTransitioning ? 0 : 0 }}
        className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 p-4"
      >
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-4">
            <HomeButton />
            
            <motion.button
              onClick={() => router.back()}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
            >
              <ArrowLeft size={20} />
              <span>Back</span>
            </motion.button>

            <div className="flex items-center gap-3">
              <motion.div
                layoutId={`mvp-avatar-${mvp.id}`}
                className="text-3xl"
                animate={!isTransitioning ? { rotate: [0, 5, 0] } : { rotate: 0 }}
                transition={!isTransitioning ? { 
                  duration: 3, 
                  repeat: Infinity 
                } : {
                  duration: 0.6,
                  ease: "easeInOut"
                }}
              >
                {mvp.avatar}
              </motion.div>
              <div>
                <motion.h1 
                  layoutId={`mvp-name-${mvp.id}`}
                  className="text-xl font-bold text-gray-900 dark:text-white"
                  transition={{ duration: 0.6, ease: "easeInOut" }}
                >
                  {mvp.name}
                </motion.h1>
                <p className="text-sm text-gray-600 dark:text-gray-400">{mvp.title}</p>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <motion.button
              onClick={() => setShowSharingModal(true)}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              title="Share chat"
            >
              <Share2 size={20} />
            </motion.button>
            
            <motion.button
              onClick={() => {
                if (confirm('Are you sure you want to clear this chat? This action cannot be undone.')) {
                  // Track chat cleared
                  track('chat_cleared', {
                    mvp_id: mvp.id,
                    mvp_name: mvp.name,
                    category_id: category?.id || 'unknown',
                    messages_cleared: messages.length
                  })
                  clearChat()
                }
              }}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
              title="Clear chat history"
            >
              <RotateCcw size={20} />
            </motion.button>
            
            <motion.button
              onClick={() => setShowInfo(!showInfo)}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
            >
              <Info size={20} />
            </motion.button>
          </div>
        </div>
      </motion.div>

      {/* Chat Messages */}
      <motion.div 
        className="flex-1 overflow-y-auto p-4"
        initial={{ opacity: isTransitioning ? 1 : 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: isTransitioning ? 0.6 : 0.2 }}
      >
        <div className="max-w-4xl mx-auto space-y-4">
          <AnimatePresence>
            {messages.map((message) => (
              <ChatMessage
                key={message.id}
                message={message}
                mvp={mvp}
                category={category}
                isStreaming={streamingMessageId === message.id}
              />
            ))}
          </AnimatePresence>
          
          {isTyping && <TypingIndicator mvp={mvp} />}
          <div ref={messagesEndRef} />
        </div>
      </motion.div>

      {/* Input */}
      <motion.div 
        className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-t border-gray-200 dark:border-gray-700 p-4"
        initial={{ opacity: isTransitioning ? 1 : 0, y: isTransitioning ? 0 : 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: isTransitioning ? 0.6 : 0.4 }}
      >
        <div className="max-w-4xl mx-auto">
          <div className="flex gap-3 items-end">
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault()
                  handleSendMessage()
                }
              }}
              placeholder={`Ask ${mvp.name} anything...`}
              disabled={!canSendMessage}
              rows={1}
              className="flex-1 px-4 py-3 rounded-2xl border border-gray-200 dark:border-gray-600 bg-white/90 dark:bg-gray-700/90 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 resize-none min-h-[48px] max-h-32 overflow-y-auto"
              style={{
                lineHeight: '1.5',
                height: 'auto'
              }}
              onInput={(e) => {
                const target = e.target as HTMLTextAreaElement
                target.style.height = 'auto'
                target.style.height = `${Math.min(target.scrollHeight, 128)}px`
              }}
            />
            <motion.button
              onClick={handleSendMessage}
              disabled={!canSendMessage || !input.trim()}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-6 py-3 bg-blue-500 text-white rounded-full hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
            >
              <Send size={18} />
            </motion.button>
          </div>
          <div className="mt-2 text-center">
            <UsageCounter />
          </div>
        </div>
      </motion.div>

      {/* Chat Sharing Modal */}
      <ChatSharingModal
        isOpen={showSharingModal}
        onClose={() => setShowSharingModal(false)}
        mvpId={mvp.id}
        mvpName={mvp.name}
        messages={messages}
        onImportChat={setMessages}
      />



      {/* Info Modal */}
      <AnimatePresence>
        {showInfo && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
            onClick={() => setShowInfo(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white dark:bg-gray-800 rounded-2xl p-6 max-w-md w-full mx-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center mb-4">
                <div className="text-4xl mb-2">{mvp.avatar}</div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">{mvp.name}</h3>
                <p className="text-gray-600 dark:text-gray-400">{mvp.era}</p>
              </div>
              <p className="text-sm text-gray-700 dark:text-gray-300 mb-4">{mvp.description}</p>
              <div className="space-y-2">
                <div>
                  <strong className="text-sm text-gray-900 dark:text-gray-100">Famous Quote:</strong>
                  <p className="text-sm italic text-gray-600 dark:text-gray-400">&ldquo;{mvp.famous_quotes[0]}&rdquo;</p>
                </div>
                <div>
                  <strong className="text-sm text-gray-900 dark:text-gray-100">Expertise:</strong>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{mvp.expertise.join(', ')}</p>
                </div>
              </div>
              <button
                onClick={() => setShowInfo(false)}
                className="w-full mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Close
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </main>
  )
}