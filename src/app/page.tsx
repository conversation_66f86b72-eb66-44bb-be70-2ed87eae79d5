'use client'

import { motion } from 'framer-motion'
import { CategoryPortal } from '@/components/CategoryPortal'
import { UsageCounter } from '@/components/UsageCounter'
import { ThemeToggle } from '@/components/ThemeToggle'
import { categories } from '@/data/categories'

export default function Home() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 p-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="max-w-7xl mx-auto"
      >
        {/* Header */}
        <div className="text-center mb-16 relative">
          <div className="absolute top-0 right-0">
            <ThemeToggle />
          </div>
          <motion.h1 
            className="text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4"
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            VOX VITAE
          </motion.h1>
          <motion.p 
            className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            Journey through time and engage in authentic conversations with history&apos;s greatest minds
          </motion.p>
        </div>

        {/* Category Portals */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {categories.map((category, index) => (
            <CategoryPortal 
              key={category.id} 
              category={category} 
              index={index}
            />
          ))}
        </div>

        {/* Usage Counter */}
        <UsageCounter />
      </motion.div>
    </main>
  )
}