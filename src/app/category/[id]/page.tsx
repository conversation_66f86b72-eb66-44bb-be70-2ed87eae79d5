'use client'

import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { ArrowLeft } from 'lucide-react'
import { use as React_use, useEffect } from 'react'
import { track } from '@vercel/analytics'
import { categories } from '@/data/categories'
import { getMVPsByCategory } from '@/data/mvps'
import { MVPCard } from '@/components/MVPCard'
import { UsageCounter } from '@/components/UsageCounter'
import { HomeButton } from '@/components/HomeButton'
import { useCategoryTransition } from '@/contexts/CategoryTransitionContext'

export default function CategoryPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter()
  const resolvedParams = React_use(params)
  const category = categories.find(c => c.id === resolvedParams.id)
  const mvps = getMVPsByCategory(resolvedParams.id)
  const { isTransitioning, transitionCategory, endCategoryTransition } = useCategoryTransition()

  // Track category page view
  useEffect(() => {
    if (category) {
      track('category_viewed', {
        category_id: category.id,
        category_name: category.name,
        mvp_count: mvps.length
      })
    }
  }, [category, mvps.length])

  // End transition after component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      endCategoryTransition()
    }, 600)
    return () => clearTimeout(timer)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []) // Remove endCategoryTransition from dependencies

  if (!category) {
    return <div>Category not found</div>
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 p-8">
      {/* Transition Overlay */}
      {isTransitioning && transitionCategory && (
        <motion.div
          className={`
            fixed inset-0 z-50
            bg-gradient-to-br ${transitionCategory.bgGradient}
            flex items-center justify-center
          `}
          initial={{ opacity: 1 }}
          animate={{ opacity: 0 }}
          transition={{ duration: 0.6, ease: "easeInOut" }}
        >
          <div className="text-center">
            <motion.div
              layoutId={`category-icon-${transitionCategory.id}`}
              className="text-8xl mb-6"
            >
              {transitionCategory.icon}
            </motion.div>
            <motion.h1
              layoutId={`category-title-${transitionCategory.id}`}
              className="text-4xl font-bold text-gray-900 dark:text-white"
            >
              {transitionCategory.name}
            </motion.h1>
          </div>
        </motion.div>
      )}

      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: isTransitioning ? 1 : 0, y: isTransitioning ? 0 : -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: isTransitioning ? 0 : 0 }}
          className="flex items-center justify-between mb-8"
        >
          <HomeButton />
          <motion.button
            onClick={() => router.back()}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            <ArrowLeft size={20} />
            <span>Back to Categories</span>
          </motion.button>
        </motion.div>

        {/* Category Title */}
        <motion.div
          initial={{ opacity: isTransitioning ? 1 : 0, y: isTransitioning ? 0 : 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: isTransitioning ? 0 : 0.2, duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center gap-4 mb-4">
            <motion.div
              layoutId={`category-icon-${category.id}`}
              className="text-5xl"
              animate={!isTransitioning ? { rotate: [0, 10, 0] } : { rotate: 0 }}
              transition={!isTransitioning ? { 
                duration: 4, 
                repeat: Infinity, 
                ease: "easeInOut"
              } : {
                duration: 0.6,
                ease: "easeInOut"
              }}
            >
              {category.icon}
            </motion.div>
            <motion.h1 
              layoutId={`category-title-${category.id}`}
              className="text-4xl font-bold text-gray-900 dark:text-white"
              transition={{ duration: 0.6, ease: "easeInOut" }}
            >
              {category.name}
            </motion.h1>
          </div>
          <motion.p 
            className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto"
            initial={{ opacity: isTransitioning ? 1 : 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: isTransitioning ? 0 : 0.4, duration: 0.6 }}
          >
            {category.description}
          </motion.p>
        </motion.div>

        {/* MVP Grid */}
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12"
          initial={{ opacity: isTransitioning ? 1 : 0, y: isTransitioning ? 0 : 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: isTransitioning ? 0.6 : 0.4, duration: 0.6 }}
        >
          {mvps.map((mvp, index) => (
            <MVPCard 
              key={mvp.id} 
              mvp={mvp} 
              index={isTransitioning ? index + 5 : index}
              category={category}
            />
          ))}
        </motion.div>

        {/* Usage Counter */}
        <UsageCounter />
      </div>
    </main>
  )
}