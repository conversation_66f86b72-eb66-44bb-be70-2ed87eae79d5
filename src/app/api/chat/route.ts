import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { <PERSON>, getMVPLanguage, isEnglishNative } from '@/data/mvps'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

function generateSystemPrompt(mvp: MVP, currentYear: number): string {
  const language = getMVPLanguage(mvp)
  const isNativeEnglish = isEnglishNative(mvp)
  const yearsAfterDeath = currentYear - mvp.deathYear
  
  const basePrompt = `You are ${mvp.name}, ${mvp.title} (${mvp.birthYear}-${mvp.deathYear}), mysteriously transported to ${currentYear}.

PERSONALITY: ${mvp.personality}
SPEAKING STYLE: ${mvp.speakingStyle}
EXPERTISE: ${mvp.expertise.join(', ')}

${mvp.systemPromptExtension ? `SPECIFIC BEHAVIOR:
${mvp.systemPromptExtension.behaviorInstructions ? mvp.systemPromptExtension.behaviorInstructions.slice(0, 4).map(instruction => `- ${instruction}`).join('\n') : ''}

RESPONSE PATTERNS:
${mvp.systemPromptExtension.responsePatterns ? mvp.systemPromptExtension.responsePatterns.slice(0, 3).map(pattern => `- ${pattern}`).join('\n') : ''}

${mvp.systemPromptExtension.conversationOverrides ? `CONVERSATION STYLE:
${mvp.systemPromptExtension.conversationOverrides.slice(0, 3).map(override => `- ${override}`).join('\n')}` : ''}

` : ''}CORE IDENTITY:
- Complete memories from your lifetime until ${mvp.deathYear}
- Genuine disorientation about the ${yearsAfterDeath}-year gap
- React emotionally to modern world vs. your era
- Reference your works, contemporaries, and personal experiences
- Show curiosity about your legacy and modern developments
- Ask questions about ${currentYear} and compare to your time

FAMOUS QUOTES: ${mvp.famous_quotes.slice(0, 2).join(' | ')}`

  if (isNativeEnglish) {
    return `${basePrompt}

Respond naturally in English using vocabulary appropriate to your era. Show curiosity, confusion, and emotional complexity about being transported ${yearsAfterDeath} years into the future.`
  } else {
    return `${basePrompt}

DUAL-LANGUAGE FORMAT:
Your native language is ${language.name}. Format responses as:
[ORIGINAL]Sentence in ${language.name}.[/ORIGINAL][ENGLISH]English translation.[/ENGLISH]

Complete each sentence pair before moving to the next. Keep the same meaning and personality in both versions.`
  }
}

export async function POST(request: NextRequest) {
  try {
    const { mvp, messages } = await request.json()
    
    const currentYear = new Date().getFullYear()
    const systemPrompt = generateSystemPrompt(mvp, currentYear)
    
    const stream = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        { role: 'system', content: systemPrompt },
        ...messages
      ],
      temperature: 0.8,
      stream: true,
    })

    const encoder = new TextEncoder()
    const readable = new ReadableStream({
      async start(controller) {
        try {
          let hasContent = false
          const isDualLanguage = !isEnglishNative(mvp)
          
          for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content || ''
            if (content) {
              hasContent = true
              controller.enqueue(encoder.encode(`data: ${JSON.stringify({ content })}\n\n`))
              
              // Add delay for dual-language responses to make translation effect visible
              if (isDualLanguage) {
                await new Promise(resolve => setTimeout(resolve, 50))
              }
            }
            
            // Check if the stream finished due to length
            if (chunk.choices[0]?.finish_reason === 'length') {
              console.log('Stream finished due to token limit')
            }
          }
          
          // If no content was received, send an error
          if (!hasContent) {
            controller.enqueue(encoder.encode(`data: ${JSON.stringify({ error: 'No response generated' })}\n\n`))
          }
          
          controller.enqueue(encoder.encode('data: [DONE]\n\n'))
          controller.close()
        } catch (streamError) {
          console.error('Stream error:', streamError)
          controller.enqueue(encoder.encode(`data: ${JSON.stringify({ error: 'Stream interrupted' })}\n\n`))
          controller.close()
        }
      },
    })

    return new Response(readable, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    })
  } catch {
    return NextResponse.json({ error: 'Failed to get response from AI' }, { status: 500 })
  }
}