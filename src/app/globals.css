@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

@layer utilities {
  .portal-3d {
    perspective: 1000px;
    transform-style: preserve-3d;
  }
  
  .portal-card {
    transform-style: preserve-3d;
    transition: transform 0.6s ease;
  }
  
  .portal-card:hover {
    transform: rotateX(5deg) rotateY(5deg) scale(1.05);
  }
}