import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { ThemeProvider } from '@/contexts/ThemeContext'
import { LayoutTransitionProvider } from '@/contexts/LayoutTransitionContext'
import { CategoryTransitionProvider } from '@/contexts/CategoryTransitionContext'
import { Analytics } from '@vercel/analytics/react'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Vox Vitae - Chat with History\'s Greatest Minds',
  description: 'Journey through time and engage in authentic conversations with the 100 most influential figures in human history.',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={`${inter.className} bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors`}>
        <ThemeProvider>
          <LayoutTransitionProvider>
            <CategoryTransitionProvider>
              {children}
            </CategoryTransitionProvider>
          </LayoutTransitionProvider>
        </ThemeProvider>
        <Analytics />
      </body>
    </html>
  )
}