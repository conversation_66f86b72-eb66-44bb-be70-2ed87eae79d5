/**
 * Sentence Tracker Utility
 * Manages sentence completion states and translation timing
 */

import { SentenceData, SentenceState, SentenceEvent } from '@/types/dual-language'

export class SentenceTracker {
  private sentences: Map<string, SentenceData> = new Map()
  private eventCallbacks: ((event: SentenceEvent) => void)[] = []
  private translationTimers: Map<string, NodeJS.Timeout> = new Map()

  constructor(private config: {
    translationDelay: number
    translationTimeout: number
    enableDebugMode: boolean
  }) {}

  /**
   * Add or update a sentence
   */
  addSentence(sentence: SentenceData): void {
    const existing = this.sentences.get(sentence.id)
    
    if (existing) {
      // Update existing sentence
      const updated = {
        ...existing,
        ...sentence,
        lastUpdated: Date.now()
      }
      this.sentences.set(sentence.id, updated)
      
      // Check if sentence just became complete
      if (!existing.isComplete && sentence.isComplete && sentence.state === 'complete') {
        if (this.config.enableDebugMode) {
          console.log('SentenceTracker: Sentence became complete:', sentence.id)
        }
        this.emitEvent({ type: 'sentence_completed', sentenceId: sentence.id })
      }
    } else {
      // Add new sentence
      const newSentence = {
        ...sentence,
        timestamp: Date.now()
      }
      this.sentences.set(sentence.id, newSentence)
      
      // Check if new sentence is already complete
      if (sentence.isComplete && sentence.state === 'complete') {
        if (this.config.enableDebugMode) {
          console.log('SentenceTracker: New sentence is complete:', sentence.id)
        }
        this.emitEvent({ type: 'sentence_completed', sentenceId: sentence.id })
      }
    }

    // Check if sentence is complete and should start translation
    if (sentence.isComplete && sentence.state === 'complete' && sentence.shouldTranslate) {
      this.scheduleTranslation(sentence.id)
    }
  }

  /**
   * Mark a sentence as complete
   */
  markSentenceComplete(sentenceId: string): void {
    const sentence = this.sentences.get(sentenceId)
    if (!sentence) return

    const updatedSentence: SentenceData = {
      ...sentence,
      isComplete: true,
      state: 'complete',
      timestamp: Date.now()
    }

    this.sentences.set(sentenceId, updatedSentence)
    this.emitEvent({ type: 'sentence_completed', sentenceId })

    // Schedule translation if needed
    if (updatedSentence.shouldTranslate) {
      this.scheduleTranslation(sentenceId)
    }
  }

  /**
   * Schedule translation for a sentence
   */
  private scheduleTranslation(sentenceId: string): void {
    const sentence = this.sentences.get(sentenceId)
    if (!sentence || sentence.state === 'translated') return

    // Clear any existing timer
    const existingTimer = this.translationTimers.get(sentenceId)
    if (existingTimer) {
      clearTimeout(existingTimer)
    }

    // Schedule new translation
    const timer = setTimeout(() => {
      this.startTranslation(sentenceId)
    }, sentence.translationDelay)

    this.translationTimers.set(sentenceId, timer)
  }

  /**
   * Start translation for a sentence
   */
  startTranslation(sentenceId: string): void {
    const sentence = this.sentences.get(sentenceId)
    if (!sentence || sentence.state === 'translated') return

    // Update sentence state
    const updatedSentence: SentenceData = {
      ...sentence,
      state: 'translated',
      timestamp: Date.now()
    }

    this.sentences.set(sentenceId, updatedSentence)
    this.emitEvent({ type: 'translation_started', sentenceId })

    // Set timeout for translation completion
    const timeoutTimer = setTimeout(() => {
      this.completeTranslation(sentenceId)
    }, this.config.translationTimeout)

    this.translationTimers.set(`${sentenceId}_timeout`, timeoutTimer)
  }

  /**
   * Complete translation for a sentence
   */
  completeTranslation(sentenceId: string): void {
    const sentence = this.sentences.get(sentenceId)
    if (!sentence) return

    this.emitEvent({ type: 'sentence_translated', sentenceId })
    
    // Clear timers
    this.clearTimers(sentenceId)
  }

  /**
   * Handle translation failure
   */
  failTranslation(sentenceId: string, error: string): void {
    const sentence = this.sentences.get(sentenceId)
    if (!sentence) return

    const updatedSentence: SentenceData = {
      ...sentence,
      state: 'error',
      timestamp: Date.now()
    }

    this.sentences.set(sentenceId, updatedSentence)
    this.emitEvent({ type: 'translation_failed', sentenceId, error })
    
    // Clear timers
    this.clearTimers(sentenceId)
  }

  /**
   * Clear all timers for a sentence
   */
  private clearTimers(sentenceId: string): void {
    const timer = this.translationTimers.get(sentenceId)
    const timeoutTimer = this.translationTimers.get(`${sentenceId}_timeout`)
    
    if (timer) {
      clearTimeout(timer)
      this.translationTimers.delete(sentenceId)
    }
    
    if (timeoutTimer) {
      clearTimeout(timeoutTimer)
      this.translationTimers.delete(`${sentenceId}_timeout`)
    }
  }

  /**
   * Get all sentences as array
   */
  getSentences(): SentenceData[] {
    return Array.from(this.sentences.values()).sort((a, b) => a.startIndex - b.startIndex)
  }

  /**
   * Get sentences in specific state
   */
  getSentencesByState(state: SentenceState): SentenceData[] {
    return this.getSentences().filter(sentence => sentence.state === state)
  }

  /**
   * Get active sentences (incomplete or complete but not translated)
   */
  getActiveSentences(): SentenceData[] {
    return this.getSentences().filter(sentence => 
      sentence.state === 'incomplete' || sentence.state === 'complete'
    )
  }

  /**
   * Get pending translations
   */
  getPendingTranslations(): SentenceData[] {
    return this.getSentences().filter(sentence => 
      sentence.state === 'complete' && sentence.shouldTranslate
    )
  }

  /**
   * Update multiple sentences
   */
  updateSentences(sentences: SentenceData[]): void {
    sentences.forEach(sentence => {
      this.addSentence(sentence)
    })
  }

  /**
   * Clear all sentences
   */
  clear(): void {
    // Clear all timers
    this.translationTimers.forEach(timer => clearTimeout(timer))
    this.translationTimers.clear()
    
    // Clear sentences
    this.sentences.clear()
  }

  /**
   * Add event callback
   */
  addEventListener(callback: (event: SentenceEvent) => void): void {
    this.eventCallbacks.push(callback)
  }

  /**
   * Remove event callback
   */
  removeEventListener(callback: (event: SentenceEvent) => void): void {
    const index = this.eventCallbacks.indexOf(callback)
    if (index > -1) {
      this.eventCallbacks.splice(index, 1)
    }
  }

  /**
   * Emit event to all listeners
   */
  private emitEvent(event: SentenceEvent): void {
    this.eventCallbacks.forEach(callback => {
      try {
        callback(event)
      } catch (error) {
        if (this.config.enableDebugMode) {
          console.error('Error in sentence event callback:', error)
        }
      }
    })
  }

  /**
   * Get debug information
   */
  getDebugInfo(): Record<string, unknown> {
    return {
      totalSentences: this.sentences.size,
      activeTimers: this.translationTimers.size,
      sentenceStates: this.getSentenceStates(),
      eventCallbacks: this.eventCallbacks.length
    }
  }

  /**
   * Get sentence states summary
   */
  private getSentenceStates(): Record<SentenceState, number> {
    const states: Record<SentenceState, number> = {
      incomplete: 0,
      complete: 0,
      translated: 0,
      error: 0
    }

    this.sentences.forEach(sentence => {
      states[sentence.state]++
    })

    return states
  }
} 