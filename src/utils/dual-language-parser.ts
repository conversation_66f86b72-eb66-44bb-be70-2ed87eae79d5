/**
 * Dual Language Parser
 * Robust parsing of dual language content with streaming support
 */

import { 
  DualLanguageContent, 
  ParseResult, 
  SentenceData, 
  LanguageInfo, 
  ParserConfig
} from '@/types/dual-language'
import { getMVPLanguage, getMVPById } from '@/data/mvps'

export class DualLanguageParser {
  private config: ParserConfig
  private debugMode: boolean

  constructor(config: Partial<ParserConfig> = {}) {
    this.config = {
      translationDelay: 2000,
      maxParseAttempts: 3,
      enableDebugMode: false,
      fallbackToOriginal: true,
      sentenceMinLength: 3,
      translationTimeout: 500,
      ...config
    }
    this.debugMode = this.config.enableDebugMode
  }

  /**
   * Parse dual language content from streaming text
   */
  parse(content: string, mvpId: string, isStreaming: boolean = false): ParseResult {
    const startTime = Date.now()
    const language = this.getLanguageInfo(mvpId)
    const errors: string[] = []
    
    try {
      // Extract dual language sections
      const dualContent = this.extractDualLanguageContent(content)
      
      // Parse sentences using the new sentence-by-sentence approach
      const sentences = this.parseSentencePairs(content, language)
      
      const result: ParseResult = {
        success: true,
        content: dualContent,
        sentences,
        errors,
        debugInfo: this.debugMode ? {
          parseTime: Date.now() - startTime,
          contentLength: content.length,
          language: language.name,
          isStreaming,
          sentenceCount: sentences.length
        } : undefined
      }

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown parsing error'
      errors.push(errorMessage)
      
      if (this.debugMode) {
        console.error('DualLanguageParser error:', error)
      }

      // Return fallback result
      return this.createFallbackResult(content, language, errors)
    }
  }

  /**
   * Extract dual language content from raw text with sentence-by-sentence parsing
   */
  private extractDualLanguageContent(content: string): DualLanguageContent {
    // Extract all sentence pairs: [ORIGINAL]...[/ORIGINAL][ENGLISH]...[/ENGLISH]
    const sentencePairPattern = /\[ORIGINAL\](.*?)\[\/ORIGINAL\]\s*(?:\[ENGLISH\](.*?)\[\/ENGLISH\])?/g
    const matches: RegExpExecArray[] = []
    let match
    while ((match = sentencePairPattern.exec(content)) !== null) {
      matches.push(match)
    }
    
    let originalText = ''
    let englishText = ''
    
    matches.forEach(match => {
      const original = match[1]?.trim() || ''
      const english = match[2]?.trim() || ''
      
      if (original) {
        originalText += (originalText ? ' ' : '') + original
      }
      if (english) {
        englishText += (englishText ? ' ' : '') + english
      }
    })
    
    // Also handle incomplete patterns during streaming
    const incompleteOriginalPattern = /\[ORIGINAL\]([^[]*?)(?:\[\/ORIGINAL\])?$/
    const incompleteMatch = content.match(incompleteOriginalPattern)
    
    if (incompleteMatch && !matches.length) {
      originalText = incompleteMatch[1]?.trim() || ''
    }
    
    // Check for valid dual language format
    const hasValidFormat = Boolean(matches.length > 0 || content.includes('[ORIGINAL]'))
    
    // Determine completion states
    const isOriginalComplete = content.includes('[/ORIGINAL]') || content.endsWith('[/ORIGINAL]')
    const isEnglishComplete = content.includes('[/ENGLISH]') || content.endsWith('[/ENGLISH]')
    
    return {
      originalText,
      englishText,
      isOriginalComplete,
      isEnglishComplete,
      hasValidFormat,
      parseErrors: []
    }
  }

  /**
   * Check if content section is complete
   */
  private isContentComplete(text: string, fullContent: string, section: 'original' | 'english'): boolean {
    const endTag = section === 'original' ? '[/ORIGINAL]' : '[/ENGLISH]'
    return fullContent.includes(endTag) || (!text && section === 'english')
  }

  /**
   * Parse sentences from dual language content using sentence-by-sentence format
   */
  private parseSentences(
    content: DualLanguageContent, 
    language: LanguageInfo
  ): SentenceData[] {
    // Parse sentence pairs directly from the content
    const rawContent = content.originalText + content.englishText
    return this.parseSentencePairs(rawContent, language)
  }

  /**
   * Parse sentence pairs from raw content
   */
     private parseSentencePairs(content: string, language: LanguageInfo): SentenceData[] {
    const sentences: SentenceData[] = []
    let currentIndex = 0
    
    // Extract completed sentence pairs - allow whitespace/newlines between tags
    const sentencePairPattern = /\[ORIGINAL\](.*?)\[\/ORIGINAL\]\s*(?:\[ENGLISH\](.*?)\[\/ENGLISH\])?/g
    let match
    let sentenceIndex = 0
    
    while ((match = sentencePairPattern.exec(content)) !== null) {
      const originalText = match[1]?.trim() || ''
      const englishText = match[2]?.trim() || ''
      
      if (originalText) {
        const sentence: SentenceData = {
          id: `sentence-${sentenceIndex}`,
          originalText,
          englishText,
          state: 'complete', // Always start as complete, let tracker handle translation
          isComplete: true,
          shouldTranslate: Boolean(englishText && originalText.length >= this.config.sentenceMinLength),
          translationDelay: this.config.translationDelay,
          startIndex: currentIndex,
          endIndex: currentIndex + originalText.length,
          timestamp: Date.now()
        }
        
        sentences.push(sentence)
        currentIndex += originalText.length
        sentenceIndex++
      }
    }
    
    // Handle incomplete sentence at the end
    const incompletePattern = /\[ORIGINAL\]([^[]*?)(?:\[\/ORIGINAL\])?$/
    const incompleteMatch = content.match(incompletePattern)
    
    if (incompleteMatch) {
      const originalText = incompleteMatch[1]?.trim() || ''
      
      if (originalText && !sentences.some(s => s.originalText === originalText)) {
        const endsWithPunctuation = language.punctuationPattern.test(originalText)
        
        const sentence: SentenceData = {
          id: `sentence-${sentenceIndex}`,
          originalText,
          englishText: '',
          state: endsWithPunctuation ? 'complete' : 'incomplete',
          isComplete: endsWithPunctuation,
          shouldTranslate: endsWithPunctuation && originalText.length >= this.config.sentenceMinLength,
          translationDelay: this.config.translationDelay,
          startIndex: currentIndex,
          endIndex: currentIndex + originalText.length,
          timestamp: Date.now()
        }
        
        sentences.push(sentence)
      }
    }
    
    return sentences
  }

  /**
   * Split text into sentences using language-specific patterns
   */
  private splitIntoSentences(text: string, language: LanguageInfo): string[] {
    if (!text.trim()) return []
    
    // Use language-specific sentence end pattern
    const pattern = language.sentenceEndPattern || /[.!?;:]\s+/g
    const sentences: string[] = []
    let lastIndex = 0
    let match
    
    // Reset the regex lastIndex to avoid issues with global regex
    pattern.lastIndex = 0
    
    while ((match = pattern.exec(text)) !== null) {
      const sentence = text.slice(lastIndex, match.index + match[0].length).trim()
      if (sentence) {
        sentences.push(sentence)
      }
      lastIndex = match.index + match[0].length
    }
    
    // Add remaining text as incomplete sentence
    const remainingText = text.slice(lastIndex).trim()
    if (remainingText) {
      sentences.push(remainingText)
    }
    
    return sentences.length > 0 ? sentences : [text]
  }

  /**
   * Get language information with enhanced patterns
   */
  private getLanguageInfo(mvpId: string): LanguageInfo {
    const mvp = getMVPById(mvpId)
    if (!mvp) {
      throw new Error(`MVP with ID ${mvpId} not found`)
    }
    const baseLanguage = getMVPLanguage(mvp)
    
    // Enhanced language-specific patterns
    const languagePatterns: Record<string, { punctuation: RegExp; sentenceEnd: RegExp }> = {
      'grc': { // Ancient Greek
        punctuation: /[.!?;:·]/g,
        sentenceEnd: /[.!?;:·]\s+/g
      },
      'la': { // Latin
        punctuation: /[.!?;:]/g,
        sentenceEnd: /[.!?;:]\s+/g
      },
      'fr': { // French
        punctuation: /[.!?;:]/g,
        sentenceEnd: /[.!?;:]\s+/g
      },
      'it': { // Italian
        punctuation: /[.!?;:]/g,
        sentenceEnd: /[.!?;:]\s+/g
      },
      'sr': { // Serbian
        punctuation: /[.!?;:]/g,
        sentenceEnd: /[.!?;:]\s+/g
      },
      'zh-classical': { // Classical Chinese
        punctuation: /[。！？；：]/g,
        sentenceEnd: /[。！？；：]\s*/g
      },
      'default': {
        punctuation: /[.!?;:]/g,
        sentenceEnd: /[.!?;:]\s+/g
      }
    }
    
    const patterns = languagePatterns[baseLanguage.code] || languagePatterns['default']
    
    return {
      ...baseLanguage,
      punctuationPattern: patterns.punctuation,
      sentenceEndPattern: patterns.sentenceEnd
    }
  }

  /**
   * Create fallback result when parsing fails
   */
  private createFallbackResult(
    content: string, 
    language: LanguageInfo, 
    errors: string[]
  ): ParseResult {
    const fallbackContent: DualLanguageContent = {
      originalText: content,
      englishText: this.config.fallbackToOriginal ? content : '',
      isOriginalComplete: true,
      isEnglishComplete: this.config.fallbackToOriginal,
      hasValidFormat: false,
      parseErrors: errors
    }
    
    const fallbackSentence: SentenceData = {
      id: 'fallback-0',
      originalText: content,
      englishText: this.config.fallbackToOriginal ? content : '',
      state: this.config.fallbackToOriginal ? 'translated' : 'complete',
      isComplete: true,
      shouldTranslate: false,
      translationDelay: 0,
      startIndex: 0,
      endIndex: content.length,
      timestamp: Date.now()
    }
    
    return {
      success: false,
      content: fallbackContent,
      sentences: [fallbackSentence],
      errors,
      debugInfo: this.debugMode ? {
        fallbackUsed: true,
        originalContent: content,
        errorCount: errors.length
      } : undefined
    }
  }

  /**
   * Update parser configuration
   */
  updateConfig(newConfig: Partial<ParserConfig>): void {
    this.config = { ...this.config, ...newConfig }
    this.debugMode = this.config.enableDebugMode
  }

  /**
   * Get current configuration
   */
  getConfig(): ParserConfig {
    return { ...this.config }
  }
} 