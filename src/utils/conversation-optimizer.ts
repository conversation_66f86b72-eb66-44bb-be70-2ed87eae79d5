interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

interface OptimizedMessage {
  role: 'user' | 'assistant'
  content: string
}

/**
 * Extract English text from dual-language format
 * Input: "[ORIGINAL]Greek text[/ORIGINAL][ENGLISH]English text[/ENGLISH]"
 * Output: "English text"
 */
function extractEnglishFromDualLanguage(content: string): string {
  // Match all [ENGLISH]...[/ENGLISH] blocks
  const englishMatches = content.match(/\[ENGLISH\](.*?)\[\/ENGLISH\]/g)
  
  if (!englishMatches) {
    // No dual language format found, return original content
    return content
  }
  
  // Extract just the English text from each match
  const englishTexts = englishMatches.map(match => 
    match.replace(/\[ENGLISH\](.*?)\[\/ENGLISH\]/, '$1')
  )
  
  // Join with spaces to form complete English response
  return englishTexts.join(' ').trim()
}

/**
 * Check if a message contains dual-language format
 */
function isDualLanguageMessage(content: string): boolean {
  return content.includes('[ORIGINAL]') && content.includes('[ENGLISH]')
}

/**
 * Optimize conversation history by:
 * 1. Keeping recent messages (last N) in full dual-language format
 * 2. Converting older dual-language messages to English-only
 * 3. Preserving user messages as-is
 * 4. Maintaining conversation flow and context
 */
export function optimizeConversationHistory(
  messages: Message[], 
  recentMessageCount: number = 6 // Keep last 6 messages in full format
): OptimizedMessage[] {
  if (messages.length === 0) return []
  
  // Split messages into recent and older
  const totalMessages = messages.length
  const cutoffIndex = Math.max(0, totalMessages - recentMessageCount)
  
  const olderMessages = messages.slice(0, cutoffIndex)
  const recentMessages = messages.slice(cutoffIndex)
  
  // Process older messages - strip dual language format
  const optimizedOlderMessages: OptimizedMessage[] = olderMessages.map(msg => ({
    role: msg.role,
    content: msg.role === 'assistant' && isDualLanguageMessage(msg.content)
      ? extractEnglishFromDualLanguage(msg.content)
      : msg.content
  }))
  
  // Keep recent messages as-is to maintain current conversation context
  const optimizedRecentMessages: OptimizedMessage[] = recentMessages.map(msg => ({
    role: msg.role,
    content: msg.content
  }))
  
  return [...optimizedOlderMessages, ...optimizedRecentMessages]
}

/**
 * Calculate approximate token savings from optimization
 */
export function calculateTokenSavings(messages: Message[]): {
  originalApproxTokens: number
  optimizedApproxTokens: number
  savings: number
  savingsPercentage: number
} {
  const originalTokens = messages.reduce((total, msg) => total + estimateTokens(msg.content), 0)
  const optimized = optimizeConversationHistory(messages)
  const optimizedTokens = optimized.reduce((total, msg) => total + estimateTokens(msg.content), 0)
  
  const savings = originalTokens - optimizedTokens
  const savingsPercentage = originalTokens > 0 ? (savings / originalTokens) * 100 : 0
  
  return {
    originalApproxTokens: originalTokens,
    optimizedApproxTokens: optimizedTokens,
    savings,
    savingsPercentage
  }
}

/**
 * Rough token estimation (approximately 4 characters per token)
 */
function estimateTokens(text: string): number {
  return Math.ceil(text.length / 4)
}

/**
 * Get optimization stats for debugging/analytics
 */
export function getOptimizationStats(messages: Message[], recentCount: number = 6): {
  totalMessages: number
  recentMessages: number
  olderMessages: number
  dualLanguageMessagesOptimized: number
  tokenSavings: ReturnType<typeof calculateTokenSavings>
} {
  const totalMessages = messages.length
  const cutoffIndex = Math.max(0, totalMessages - recentCount)
  const olderMessages = messages.slice(0, cutoffIndex)
  
  const dualLanguageMessagesOptimized = olderMessages.filter(msg => 
    msg.role === 'assistant' && isDualLanguageMessage(msg.content)
  ).length
  
  return {
    totalMessages,
    recentMessages: totalMessages - cutoffIndex,
    olderMessages: cutoffIndex,
    dualLanguageMessagesOptimized,
    tokenSavings: calculateTokenSavings(messages)
  }
} 