/**
 * Dual Language Message System Types
 * Comprehensive type definitions for streaming dual language parsing
 */

// Core parsing states
export type ParsingState = 'idle' | 'parsing_original' | 'parsing_english' | 'complete' | 'error'

// Streaming phases
export type StreamingPhase = 'waiting' | 'streaming' | 'complete'

// Sentence completion states
export type SentenceState = 'incomplete' | 'complete' | 'translated' | 'error'

// Language detection result
export interface LanguageInfo {
  code: string
  name: string
  nativeName: string
  direction: 'ltr' | 'rtl'
  punctuationPattern: RegExp
  sentenceEndPattern: RegExp
}

// Individual sentence tracking
export interface SentenceData {
  id: string
  originalText: string
  englishText: string
  state: SentenceState
  isComplete: boolean
  shouldTranslate: boolean
  translationDelay: number
  startIndex: number
  endIndex: number
  timestamp: number
  lastUpdated?: number
}

// Parsed dual language content
export interface DualLanguageContent {
  originalText: string
  englishText: string
  isOriginalComplete: boolean
  isEnglishComplete: boolean
  hasValidFormat: boolean
  parseErrors: string[]
}

// Streaming message state
export interface StreamingMessage {
  id: string
  content: string
  mvpId: string
  language: LanguageInfo
  phase: StreamingPhase
  parsingState: ParsingState
  parsedContent: DualLanguageContent
  sentences: SentenceData[]
  lastUpdated: number
  debugInfo?: {
    rawContent: string
    parseAttempts: number
    errors: string[]
    timings: { [key: string]: number }
  }
}

// Parser configuration
export interface ParserConfig {
  translationDelay: number
  maxParseAttempts: number
  enableDebugMode: boolean
  fallbackToOriginal: boolean
  sentenceMinLength: number
  translationTimeout: number
}

// Parser result
export interface ParseResult {
  success: boolean
  content: DualLanguageContent
  sentences: SentenceData[]
  errors: string[]
  debugInfo?: Record<string, unknown>
}

// Sentence tracker events
export type SentenceEvent = 
  | { type: 'sentence_completed'; sentenceId: string }
  | { type: 'sentence_translated'; sentenceId: string }
  | { type: 'translation_started'; sentenceId: string }
  | { type: 'translation_failed'; sentenceId: string; error: string }
  | { type: 'parsing_error'; error: string }

// Animation states
export interface AnimationState {
  isEntering: boolean
  isExiting: boolean
  isTranslating: boolean
  currentLanguage: 'original' | 'english'
  progress: number
}

// Component props
export interface DualLanguageMessageProps {
  content: string
  mvp: import('@/data/mvps').MVP
  isStreaming?: boolean
  config?: Partial<ParserConfig>
  onSentenceEvent?: (event: SentenceEvent) => void
  onParsingError?: (error: string) => void
  debugMode?: boolean
}

// Hook return types
export interface UseDualLanguageParser {
  streamingMessage: StreamingMessage
  updateContent: (content: string) => void
  setStreamingComplete: () => void
  reset: () => void
  getDebugInfo: () => Record<string, unknown>
}

export interface UseSentenceTracker {
  sentences: SentenceData[]
  updateSentences: (sentences: SentenceData[]) => void
  markSentenceComplete: (sentenceId: string) => void
  startTranslation: (sentenceId: string) => void
  completeTranslation: (sentenceId: string) => void
  getActiveSentences: () => SentenceData[]
  getPendingTranslations: () => SentenceData[]
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>> 