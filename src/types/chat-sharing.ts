export interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

export interface ExportedChat {
  // Chat metadata
  id: string
  mvpId: string
  mvpName: string
  exportedAt: Date
  version: string
  
  // Chat content
  messages: Message[]
  messageCount: number
  
  // Context for recreation
  lastUpdated: Date
  sessionInfo: {
    startedAt: Date
    totalMessages: number
    userMessageCount: number
    assistantMessageCount: number
  }
  
  // Sharing metadata
  shareInfo?: {
    sharedBy?: string
    shareNote?: string
    isPublic?: boolean
    allowContinuation?: boolean
  }
}

export interface ImportResult {
  success: boolean
  chatId?: string
  error?: string
  warnings?: string[]
  importedMessageCount?: number
}

export interface ShareOptions {
  includePersonalInfo?: boolean
  allowContinuation?: boolean
  addNote?: string
  generatePublicLink?: boolean
  expiresAt?: Date
}

export interface ChatShareLink {
  id: string
  chatId: string
  url: string
  expiresAt?: Date
  createdAt: Date
  accessCount: number
  maxAccess?: number
}

// For URL-based sharing
export interface SharedChatData {
  chat: ExportedChat
  metadata: {
    sharedAt: Date
    accessCount: number
    lastAccessed?: Date
  }
} 