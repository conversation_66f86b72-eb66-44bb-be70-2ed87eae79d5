export interface Category {
  id: string
  name: string
  description: string
  icon: string
  color: string
  bgGradient: string
  figures: string[]
}

export const categories: Category[] = [
  {
    id: 'philosophy',
    name: 'PHILOSOPHY',
    description: 'Explore the depths of human thought and wisdom',
    icon: '🏛️',
    color: 'from-blue-500 to-indigo-600',
    bgGradient: 'from-blue-50 to-indigo-100',
    figures: ['🧙‍♂️', '👨‍🏫', '🤵', '🧘‍♂️', '🎩']
  },
  {
    id: 'science',
    name: 'SCIENCE',
    description: 'Discover the laws that govern our universe',
    icon: '🔬',
    color: 'from-green-500 to-emerald-600',
    bgGradient: 'from-green-50 to-emerald-100',
    figures: ['🍎', '🧠', '🐒', '🔭', '⚡']
  },
  {
    id: 'military',
    name: 'MILITARY',
    description: 'Learn from history\'s greatest strategic minds',
    icon: '⚔️',
    color: 'from-red-500 to-rose-600',
    bgGradient: 'from-red-50 to-rose-100',
    figures: ['👑', '🏛️', '🎖️', '📚', '🐘']
  },
  {
    id: 'art',
    name: 'ART',
    description: 'Experience the beauty of human creativity',
    icon: '🎨',
    color: 'from-purple-500 to-pink-600',
    bgGradient: 'from-purple-50 to-pink-100',
    figures: ['🎨', '🗿', '🎭', '🎼', '🎹']
  },
  {
    id: 'politics',
    name: 'POLITICS',
    description: 'Understand the art of governance and leadership',
    icon: '🏛️',
    color: 'from-amber-500 to-orange-600',
    bgGradient: 'from-amber-50 to-orange-100',
    figures: ['🎩', '🕊️', '🎯', '📜', '🦅']
  }
]