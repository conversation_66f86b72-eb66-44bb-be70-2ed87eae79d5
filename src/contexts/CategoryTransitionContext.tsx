'use client'

import { createContext, useContext, useState, ReactNode, useCallback } from 'react'
import { Category } from '@/data/categories'

interface CategoryTransitionContextType {
  isTransitioning: boolean
  transitionCategory: Category | null
  startCategoryTransition: (category: Category) => void
  endCategoryTransition: () => void
}

const CategoryTransitionContext = createContext<CategoryTransitionContextType | undefined>(undefined)

export function CategoryTransitionProvider({ children }: { children: ReactNode }) {
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [transitionCategory, setTransitionCategory] = useState<Category | null>(null)

  const startCategoryTransition = useCallback((category: Category) => {
    console.log('CategoryTransition: Starting transition for', category.id)
    setTransitionCategory(category)
    setIsTransitioning(true)
  }, [])

  const endCategoryTransition = useCallback(() => {
    console.log('CategoryTransition: Ending transition in 800ms')
    setTimeout(() => {
      console.log('CategoryTransition: Transition ended, clearing state')
      setIsTransitioning(false)
      setTransitionCategory(null)
    }, 800) // Keep category data available during transition
  }, [])

  return (
    <CategoryTransitionContext.Provider 
      value={{
        isTransitioning,
        transitionCategory,
        startCategoryTransition,
        endCategoryTransition
      }}
    >
      {children}
    </CategoryTransitionContext.Provider>
  )
}

export function useCategoryTransition() {
  const context = useContext(CategoryTransitionContext)
  if (context === undefined) {
    throw new Error('useCategoryTransition must be used within a CategoryTransitionProvider')
  }
  return context
}