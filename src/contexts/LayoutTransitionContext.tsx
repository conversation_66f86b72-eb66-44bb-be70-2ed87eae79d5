'use client'

import { createContext, useContext, useState } from 'react'
import { <PERSON> } from '@/data/mvps'
import { Category } from '@/data/categories'

interface LayoutTransitionContextType {
  isTransitioning: boolean
  selectedMVP: MVP | null
  selectedCategory: Category | null
  startTransition: (mvp: MVP, category: Category) => void
  endTransition: () => void
}

const LayoutTransitionContext = createContext<LayoutTransitionContextType | undefined>(undefined)

export function LayoutTransitionProvider({ children }: { children: React.ReactNode }) {
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [selectedMVP, setSelectedMVP] = useState<MVP | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)

  const startTransition = (mvp: MVP, category: Category) => {
    setSelectedMVP(mvp)
    setSelectedCategory(category)
    setIsTransitioning(true)
  }

  const endTransition = () => {
    setIsTransitioning(false)
    // Keep selectedMVP and selectedCategory for the transition
    setTimeout(() => {
      setSelectedMVP(null)
      setSelectedCategory(null)
    }, 500) // Clear after transition completes
  }

  return (
    <LayoutTransitionContext.Provider value={{
      isTransitioning,
      selectedMVP,
      selectedCategory,
      startTransition,
      endTransition
    }}>
      {children}
    </LayoutTransitionContext.Provider>
  )
}

export function useLayoutTransition() {
  const context = useContext(LayoutTransitionContext)
  if (context === undefined) {
    throw new Error('useLayoutTransition must be used within a LayoutTransitionProvider')
  }
  return context
}