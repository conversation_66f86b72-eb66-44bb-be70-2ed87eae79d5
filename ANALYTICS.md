# Vercel Analytics Implementation

## Overview
This document outlines the analytics events implemented in the Vox Vitae application to track user interactions without capturing any chat content.

## Events Tracked

### 1. Category Portal Clicks
**Event**: `category_portal_clicked`
- **Triggered**: When users click on a category portal from the homepage
- **Data Captured**:
  - `category_id`: The ID of the category clicked
  - `category_name`: The name of the category (e.g., "Philosophy", "Science")
  - `from_homepage`: <PERSON><PERSON><PERSON> indicating the click came from the homepage

### 2. Category Page Views
**Event**: `category_viewed`
- **Triggered**: When users visit a category page
- **Data Captured**:
  - `category_id`: The ID of the category viewed
  - `category_name`: The name of the category
  - `mvp_count`: Number of MVPs available in this category

### 3. MVP Card Clicks
**Event**: `mvp_card_clicked`
- **Triggered**: When users click on an MVP card to start a chat
- **Data Captured**:
  - `mvp_id`: The ID of the MVP clicked
  - `mvp_name`: The name of the MVP (e.g., "<PERSON><PERSON>", "<PERSON>")
  - `category_id`: The category this MVP belongs to
  - `category_name`: The name of the category
  - `birth_year`: The birth year of the MVP
  - `death_year`: The death year of the MVP

### 4. Chat Session Started
**Event**: `chat_session_started`
- **Triggered**: When a user starts a new chat with an MVP
- **Data Captured**:
  - `mvp_id`: The ID of the MVP
  - `mvp_name`: The name of the MVP
  - `category_id`: The category this MVP belongs to
  - `is_new_chat`: Boolean indicating this is a new chat session

### 5. Chat Session Resumed
**Event**: `chat_session_resumed`
- **Triggered**: When a user returns to an existing chat with an MVP
- **Data Captured**:
  - `mvp_id`: The ID of the MVP
  - `mvp_name`: The name of the MVP
  - `category_id`: The category this MVP belongs to
  - `message_count`: Number of messages in the existing chat

### 6. Message Sent
**Event**: `message_sent`
- **Triggered**: When a user sends a new message to an MVP
- **Data Captured**:
  - `mvp_id`: The ID of the MVP
  - `mvp_name`: The name of the MVP
  - `category_id`: The category this MVP belongs to
  - `message_length`: Length of the message in characters (no content)
  - `total_messages`: Total number of messages in the conversation

### 7. Cached Response Used
**Event**: `message_cached_response`
- **Triggered**: When a user asks a duplicate question and gets a cached response
- **Data Captured**:
  - `mvp_id`: The ID of the MVP
  - `mvp_name`: The name of the MVP
  - `category_id`: The category this MVP belongs to
  - `message_length`: Length of the message in characters (no content)

### 8. Chat Cleared
**Event**: `chat_cleared`
- **Triggered**: When a user clears their chat history with an MVP
- **Data Captured**:
  - `mvp_id`: The ID of the MVP
  - `mvp_name`: The name of the MVP
  - `category_id`: The category this MVP belongs to
  - `messages_cleared`: Number of messages that were cleared

## Privacy Considerations

- **No Content Tracking**: We never capture or track the actual content of messages
- **Metadata Only**: Only metadata like message length, frequency, and MVP identification is tracked
- **User-Centric**: All data helps understand user behavior patterns without compromising privacy
- **Aggregate Analysis**: Data is designed for aggregate analysis of app usage patterns

## Implementation Details

- **Package**: `@vercel/analytics` - Official Vercel Analytics package
- **Client-Side**: All tracking happens on the client side using the `track()` function
- **Real-Time**: Events are sent in real-time as users interact with the app
- **Development**: Analytics work in both development and production environments

## Usage in Vercel Dashboard

Once deployed, these events will be available in the Vercel Analytics dashboard under:
- **Events**: View all custom events with their properties
- **Funnels**: Track user journey from homepage → category → MVP → chat
- **Audience**: Understand user behavior patterns and popular MVPs
- **Performance**: Monitor how different interactions affect app performance

## Files Modified

1. `/src/app/layout.tsx` - Added Analytics provider
2. `/src/components/CategoryPortal.tsx` - Homepage category clicks
3. `/src/app/category/[id]/page.tsx` - Category page views
4. `/src/components/MVPCard.tsx` - MVP card clicks
5. `/src/app/chat/[figure]/page.tsx` - Chat interactions and session management